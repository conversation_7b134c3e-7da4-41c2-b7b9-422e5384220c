2025-07-23 15:19:21 [127.0.0.1][-][gnoa088o0hon1vgqigs0tb9cu9vd163k][error][yii\db\IntegrityException] PDOException: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "employer_id" of relation "employer_logs" violates not-null constraint
DETAIL:  Failing row contains (1, null, sms_verification_failed, {"phone":"+998901234567","reason":"invalid_or_expired_code"}, null, 2025-07-23 15:19:21, null). in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(431): yii\db\Command->queryInternal()
#3 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\pgsql\Schema.php(646): yii\db\Command->queryOne()
#4 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\ActiveRecord.php(604): yii\db\pgsql\Schema->insert()
#5 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\ActiveRecord.php(570): yii\db\ActiveRecord->insertInternal()
#6 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\BaseActiveRecord.php(688): yii\db\ActiveRecord->insert()
#7 D:\OSPanel\domains\ish_top\common\models\EmployerLog.php(109): yii\db\BaseActiveRecord->save()
#8 D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php(24): app\common\models\EmployerLog::logAction()
#9 D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php(268): app\modules\employer\services\LoggingService->logAction()
#10 D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php(186): app\modules\employer\services\LoggingService->logSmsVerificationFailed()
#11 [internal function]: app\modules\employer\controllers\AuthController->actionVerifyCode()
#12 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#13 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#14 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction()
#15 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction()
#16 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest()
#17 D:\OSPanel\domains\ish_top\web\index.php(12): yii\base\Application->run()
#18 {main}

Next yii\db\IntegrityException: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "employer_id" of relation "employer_logs" violates not-null constraint
DETAIL:  Failing row contains (1, null, sms_verification_failed, {"phone":"+998901234567","reason":"invalid_or_expired_code"}, null, 2025-07-23 15:19:21, null).
The SQL being executed was: INSERT INTO "employer_logs" ("action", "context", "employer_id", "device_info", "created_at") VALUES ('sms_verification_failed', '{"phone":"+998901234567","reason":"invalid_or_expired_code"}', NULL, NULL, '2025-07-23 15:19:21') RETURNING "id" in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(431): yii\db\Command->queryInternal()
#3 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\pgsql\Schema.php(646): yii\db\Command->queryOne()
#4 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\ActiveRecord.php(604): yii\db\pgsql\Schema->insert()
#5 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\ActiveRecord.php(570): yii\db\ActiveRecord->insertInternal()
#6 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\BaseActiveRecord.php(688): yii\db\ActiveRecord->insert()
#7 D:\OSPanel\domains\ish_top\common\models\EmployerLog.php(109): yii\db\BaseActiveRecord->save()
#8 D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php(24): app\common\models\EmployerLog::logAction()
#9 D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php(268): app\modules\employer\services\LoggingService->logAction()
#10 D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php(186): app\modules\employer\services\LoggingService->logSmsVerificationFailed()
#11 [internal function]: app\modules\employer\controllers\AuthController->actionVerifyCode()
#12 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#13 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#14 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction()
#15 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction()
#16 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest()
#17 D:\OSPanel\domains\ish_top\web\index.php(12): yii\base\Application->run()
#18 {main}
Additional Information:
Array
(
    [0] => 23502
    [1] => 7
    [2] => ERROR:  null value in column "employer_id" of relation "employer_logs" violates not-null constraint
DETAIL:  Failing row contains (1, null, sms_verification_failed, {"phone":"+998901234567","reason":"invalid_or_expired_code"}, null, 2025-07-23 15:19:21, null).
)

2025-07-23 15:19:21 [127.0.0.1][-][gnoa088o0hon1vgqigs0tb9cu9vd163k][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSESSID' => 'gnoa088o0hon1vgqigs0tb9cu9vd163k'
]

$_SESSION = [
    '__flash' => []
]

$_SERVER = [
    'REDIRECT_STATUS' => '200'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_USER_AGENT' => 'PostmanRuntime/7.44.1'
    'HTTP_ACCEPT' => '*/*'
    'HTTP_CACHE_CONTROL' => 'no-cache'
    'HTTP_POSTMAN_TOKEN' => 'e71f4abb-7613-49b7-bb4b-0945b1ea795a'
    'HTTP_HOST' => 'vacanct'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br'
    'HTTP_CONNECTION' => 'keep-alive'
    'CONTENT_LENGTH' => '52'
    'HTTP_COOKIE' => 'PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k'
    'PATH' => 'd:\\ospanel\\modules\\php\\PHP_8.1\\ext;d:\\ospanel\\modules\\php\\PHP_8.1\\pear;d:\\ospanel\\modules\\php\\PHP_8.1\\pear\\bin;d:\\ospanel\\modules\\php\\PHP_8.1;d:\\ospanel\\modules\\wget\\bin;d:\\ospanel\\modules\\http\\Apache_2.4-PHP_8.0-8.1\\bin;d:\\ospanel\\modules\\http\\Apache_2.4-PHP_8.0-8.1;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\system32\\Wbem;C:\\WINDOWS\\SysWOW64'
    'SystemRoot' => 'C:\\WINDOWS'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'WINDIR' => 'C:\\WINDOWS'
    'SERVER_SIGNATURE' => ''
    'SERVER_SOFTWARE' => 'Apache'
    'SERVER_NAME' => 'vacanct'
    'SERVER_ADDR' => '127.0.0.1'
    'SERVER_PORT' => '80'
    'REMOTE_ADDR' => '127.0.0.1'
    'DOCUMENT_ROOT' => 'D:/OSPanel/domains/ish_top/web'
    'REQUEST_SCHEME' => 'http'
    'CONTEXT_PREFIX' => ''
    'CONTEXT_DOCUMENT_ROOT' => 'D:/OSPanel/domains/ish_top/web'
    'SERVER_ADMIN' => '[no address given]'
    'SCRIPT_FILENAME' => 'D:/OSPanel/domains/ish_top/web/index.php'
    'REMOTE_PORT' => '62204'
    'REDIRECT_URL' => '/employer/auth/verify-code'
    'GATEWAY_INTERFACE' => 'CGI/1.1'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'REQUEST_METHOD' => 'POST'
    'QUERY_STRING' => ''
    'REQUEST_URI' => '//employer/auth/verify-code'
    'SCRIPT_NAME' => '/index.php'
    'PHP_SELF' => '/index.php'
    'REQUEST_TIME_FLOAT' => 1753273160.9754
    'REQUEST_TIME' => 1753273160
]
2025-07-23 12:31:19 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request: migrate/status in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Controller.php:149
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction('status', Array)
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction('status', Array)
#2 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction('migrate/status', Array)
#3 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction('migrate/status', Array)
#4 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest(Object(yii\console\Request))
#5 D:\OSPanel\domains\ish_top\yii(20): yii\base\Application->run()
#6 {main}

Next yii\console\UnknownCommandException: Unknown command "migrate/status". in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction('migrate/status', Array)
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest(Object(yii\console\Request))
#2 D:\OSPanel\domains\ish_top\yii(20): yii\base\Application->run()
#3 {main}
2025-07-23 12:31:18 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'c++' => 'C:\\MinGW\\bin'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133728599031302925'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_30076_XAAVBBIEKUOVITKO'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'WIN-QPO53K8FFA4'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'DataGrip' => 'D:\\app\\DataGrip 2024.1.3\\bin;'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_10684_1592913036' => '1'
    'git' => 'D:\\OSPanel\\Git\\bin'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\user'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\WIN-QPO53K8FFA4'
    'nodejs' => 'C:\\Program Files\\nodejs\\node_modules\\npm\\bin'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\PowerShell\\7-preview;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Program Files\\PowerShell\\7-preview\\preview;C:\\Users\\<USER>\\.fly\\bin;;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Users\\<USER>\\.lmstudio\\bin;C:\\Users\\<USER>\\.fly\\bin;C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\scripts;D:\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL'
    'php' => 'C:\\php'
    'PhpStorm' => 'C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;'
    'POWERSHELL_DISTRIBUTION_CHANNEL' => 'MSI:Windows 10 Pro'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 154 Stepping 4, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => '9a04'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\PowerShell\\Modules;C:\\Program Files\\PowerShell\\Modules;c:\\program files\\powershell\\7-preview\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'WIN-QPO53K8FFA4'
    'USERDOMAIN_ROAMINGPROFILE' => 'WIN-QPO53K8FFA4'
    'USERNAME' => 'user'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Program Files\\Oracle\\VirtualBox\\'
    'windir' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    'GIT_PAGER' => 'cat'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'en_US.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'd:\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'D:\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'd:\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-5770b6fe87-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753273878.9517
    'REQUEST_TIME' => 1753273878
    'argv' => [
        0 => 'yii'
        1 => 'migrate/status'
    ]
    'argc' => 2
]
2025-07-23 15:38:58 [127.0.0.1][-][gnoa088o0hon1vgqigs0tb9cu9vd163k][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request: employer/worker/list in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Controller.php:149
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction()
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction()
#2 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest()
#3 D:\OSPanel\domains\ish_top\web\index.php(12): yii\base\Application->run()
#4 {main}

Next yii\web\NotFoundHttpException: Page not found. in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest()
#1 D:\OSPanel\domains\ish_top\web\index.php(12): yii\base\Application->run()
#2 {main}
2025-07-23 15:38:58 [127.0.0.1][-][gnoa088o0hon1vgqigs0tb9cu9vd163k][info][application] $_GET = [
    'page' => '1'
    'limit' => '10'
]

$_POST = []

$_FILES = []

$_COOKIE = [
    'PHPSESSID' => 'gnoa088o0hon1vgqigs0tb9cu9vd163k'
]

$_SESSION = [
    '__flash' => []
]

$_SERVER = [
    'REDIRECT_STATUS' => '200'
    'HTTP_USER_AGENT' => 'PostmanRuntime/7.44.1'
    'HTTP_ACCEPT' => '*/*'
    'HTTP_CACHE_CONTROL' => 'no-cache'
    'HTTP_POSTMAN_TOKEN' => 'ea9f3bfb-51f3-4e07-8d8d-1a9034fd6fc4'
    'HTTP_HOST' => 'vacanct'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_COOKIE' => 'PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k'
    'PATH' => 'd:\\ospanel\\modules\\php\\PHP_8.1\\ext;d:\\ospanel\\modules\\php\\PHP_8.1\\pear;d:\\ospanel\\modules\\php\\PHP_8.1\\pear\\bin;d:\\ospanel\\modules\\php\\PHP_8.1;d:\\ospanel\\modules\\wget\\bin;d:\\ospanel\\modules\\http\\Apache_2.4-PHP_8.0-8.1\\bin;d:\\ospanel\\modules\\http\\Apache_2.4-PHP_8.0-8.1;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\system32\\Wbem;C:\\WINDOWS\\SysWOW64'
    'SystemRoot' => 'C:\\WINDOWS'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'WINDIR' => 'C:\\WINDOWS'
    'SERVER_SIGNATURE' => ''
    'SERVER_SOFTWARE' => 'Apache'
    'SERVER_NAME' => 'vacanct'
    'SERVER_ADDR' => '127.0.0.1'
    'SERVER_PORT' => '80'
    'REMOTE_ADDR' => '127.0.0.1'
    'DOCUMENT_ROOT' => 'D:/OSPanel/domains/ish_top/web'
    'REQUEST_SCHEME' => 'http'
    'CONTEXT_PREFIX' => ''
    'CONTEXT_DOCUMENT_ROOT' => 'D:/OSPanel/domains/ish_top/web'
    'SERVER_ADMIN' => '[no address given]'
    'SCRIPT_FILENAME' => 'D:/OSPanel/domains/ish_top/web/index.php'
    'REMOTE_PORT' => '61008'
    'REDIRECT_URL' => '/employer/worker/list'
    'REDIRECT_QUERY_STRING' => 'page=1&limit=10'
    'GATEWAY_INTERFACE' => 'CGI/1.1'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'REQUEST_METHOD' => 'GET'
    'QUERY_STRING' => 'page=1&limit=10'
    'REQUEST_URI' => '//employer/worker/list?page=1&limit=10'
    'SCRIPT_NAME' => '/index.php'
    'PHP_SELF' => '/index.php'
    'REQUEST_TIME_FLOAT' => 1753274338.5955
    'REQUEST_TIME' => 1753274338
]
2025-07-24 08:36:05 [127.0.0.1][-][-][error][app\modules\employer\controllers\WorkerController::actionSearch] Error searching workers: SQLSTATE[42P01]: Undefined table: 7 ERROR:  missing FROM-clause entry for table "DISTINCT w"
LINE 1: SELECT "DISTINCT w".*, 
               ^
The SQL being executed was: SELECT "DISTINCT w".*, 
                    CASE 
                        WHEN LOWER(p.name_uz) = LOWER('dastur') THEN 3
                        WHEN LOWER(p.name_ru) = LOWER('dastur') THEN 3
                        WHEN LOWER(p.name_en) = LOWER('dastur') THEN 3
                        WHEN LOWER(p.name_uz) LIKE LOWER('%dastur%') THEN 2
                        WHEN LOWER(p.name_ru) LIKE LOWER('%dastur%') THEN 2
                        WHEN LOWER(p.name_en) LIKE LOWER('%dastur%') THEN 2
                        ELSE 1
                    END as relevance_score
                 FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dastur\%%') OR ("p"."name_ru" ILIKE '%\%dastur\%%') OR ("p"."name_en" ILIKE '%\%dastur\%%')) ORDER BY "relevance_score" DESC, "w"."created_at" DESC LIMIT 10
    in D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php:129
2025-07-24 08:36:05 [127.0.0.1][-][-][info][application] $_GET = [
    'search' => 'dastur'
    'page' => '1'
    'limit' => '10'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'REDIRECT_STATUS' => '200'
    'HTTP_USER_AGENT' => 'PostmanRuntime/7.44.1'
    'HTTP_ACCEPT' => '*/*'
    'HTTP_CACHE_CONTROL' => 'no-cache'
    'HTTP_POSTMAN_TOKEN' => 'c6836673-1129-4e17-9a58-484156171458'
    'HTTP_HOST' => 'vacanct'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br'
    'HTTP_CONNECTION' => 'keep-alive'
    'PATH' => 'd:\\ospanel\\modules\\php\\PHP_8.1\\ext;d:\\ospanel\\modules\\php\\PHP_8.1\\pear;d:\\ospanel\\modules\\php\\PHP_8.1\\pear\\bin;d:\\ospanel\\modules\\php\\PHP_8.1;d:\\ospanel\\modules\\wget\\bin;d:\\ospanel\\modules\\http\\Apache_2.4-PHP_8.0-8.1\\bin;d:\\ospanel\\modules\\http\\Apache_2.4-PHP_8.0-8.1;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\system32\\Wbem;C:\\WINDOWS\\SysWOW64'
    'SystemRoot' => 'C:\\WINDOWS'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'WINDIR' => 'C:\\WINDOWS'
    'SERVER_SIGNATURE' => ''
    'SERVER_SOFTWARE' => 'Apache'
    'SERVER_NAME' => 'vacanct'
    'SERVER_ADDR' => '127.0.0.1'
    'SERVER_PORT' => '80'
    'REMOTE_ADDR' => '127.0.0.1'
    'DOCUMENT_ROOT' => 'D:/OSPanel/domains/ish_top/web'
    'REQUEST_SCHEME' => 'http'
    'CONTEXT_PREFIX' => ''
    'CONTEXT_DOCUMENT_ROOT' => 'D:/OSPanel/domains/ish_top/web'
    'SERVER_ADMIN' => '[no address given]'
    'SCRIPT_FILENAME' => 'D:/OSPanel/domains/ish_top/web/index.php'
    'REMOTE_PORT' => '58595'
    'REDIRECT_URL' => '/employer/worker/search'
    'REDIRECT_QUERY_STRING' => 'search=dastur&page=1&limit=10'
    'GATEWAY_INTERFACE' => 'CGI/1.1'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'REQUEST_METHOD' => 'GET'
    'QUERY_STRING' => 'search=dastur&page=1&limit=10'
    'REQUEST_URI' => '/employer/worker/search?search=dastur&page=1&limit=10'
    'SCRIPT_NAME' => '/index.php'
    'PHP_SELF' => '/index.php'
    'REQUEST_TIME_FLOAT' => 1753335365.6595
    'REQUEST_TIME' => 1753335365
]
2025-07-24 08:39:10 [127.0.0.1][-][-][error][app\modules\employer\controllers\WorkerController::actionSearch] Error searching workers: SQLSTATE[42P01]: Undefined table: 7 ERROR:  missing FROM-clause entry for table "DISTINCT w"
LINE 1: SELECT "DISTINCT w".*, 
               ^
The SQL being executed was: SELECT "DISTINCT w".*, 
                    CASE 
                        WHEN LOWER(p.name_uz) = 'dastur' THEN 3
                        WHEN LOWER(p.name_ru) = 'dastur' THEN 3
                        WHEN LOWER(p.name_en) = 'dastur' THEN 3
                        WHEN LOWER(p.name_uz) ILIKE '%dastur%' THEN 2
                        WHEN LOWER(p.name_ru) ILIKE '%dastur%' THEN 2
                        WHEN LOWER(p.name_en) ILIKE '%dastur%' THEN 2
                        ELSE 1
                    END AS relevance_score
                 FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dastur\%%') OR ("p"."name_ru" ILIKE '%\%dastur\%%') OR ("p"."name_en" ILIKE '%\%dastur\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0) ORDER BY "relevance_score" DESC, "w"."created_at" DESC LIMIT 10
    in D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php:129
2025-07-24 08:39:10 [127.0.0.1][-][-][info][application] $_GET = [
    'search' => 'dastur'
    'page' => '1'
    'limit' => '10'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'REDIRECT_STATUS' => '200'
    'HTTP_USER_AGENT' => 'PostmanRuntime/7.44.1'
    'HTTP_ACCEPT' => '*/*'
    'HTTP_CACHE_CONTROL' => 'no-cache'
    'HTTP_POSTMAN_TOKEN' => '1bc49da8-6422-4817-9618-bd816dd362c2'
    'HTTP_HOST' => 'vacanct'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br'
    'HTTP_CONNECTION' => 'keep-alive'
    'PATH' => 'd:\\ospanel\\modules\\php\\PHP_8.1\\ext;d:\\ospanel\\modules\\php\\PHP_8.1\\pear;d:\\ospanel\\modules\\php\\PHP_8.1\\pear\\bin;d:\\ospanel\\modules\\php\\PHP_8.1;d:\\ospanel\\modules\\wget\\bin;d:\\ospanel\\modules\\http\\Apache_2.4-PHP_8.0-8.1\\bin;d:\\ospanel\\modules\\http\\Apache_2.4-PHP_8.0-8.1;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\system32\\Wbem;C:\\WINDOWS\\SysWOW64'
    'SystemRoot' => 'C:\\WINDOWS'
    'COMSPEC' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW'
    'WINDIR' => 'C:\\WINDOWS'
    'SERVER_SIGNATURE' => ''
    'SERVER_SOFTWARE' => 'Apache'
    'SERVER_NAME' => 'vacanct'
    'SERVER_ADDR' => '127.0.0.1'
    'SERVER_PORT' => '80'
    'REMOTE_ADDR' => '127.0.0.1'
    'DOCUMENT_ROOT' => 'D:/OSPanel/domains/ish_top/web'
    'REQUEST_SCHEME' => 'http'
    'CONTEXT_PREFIX' => ''
    'CONTEXT_DOCUMENT_ROOT' => 'D:/OSPanel/domains/ish_top/web'
    'SERVER_ADMIN' => '[no address given]'
    'SCRIPT_FILENAME' => 'D:/OSPanel/domains/ish_top/web/index.php'
    'REMOTE_PORT' => '58984'
    'REDIRECT_URL' => '/employer/worker/search'
    'REDIRECT_QUERY_STRING' => 'search=dastur&page=1&limit=10'
    'GATEWAY_INTERFACE' => 'CGI/1.1'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'REQUEST_METHOD' => 'GET'
    'QUERY_STRING' => 'search=dastur&page=1&limit=10'
    'REQUEST_URI' => '/employer/worker/search?search=dastur&page=1&limit=10'
    'SCRIPT_NAME' => '/index.php'
    'PHP_SELF' => '/index.php'
    'REQUEST_TIME_FLOAT' => 1753335550.3414
    'REQUEST_TIME' => 1753335550
]
