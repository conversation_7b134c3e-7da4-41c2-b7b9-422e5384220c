<?php

namespace app\modules\employer\controllers;

use yii\rest\Controller;
use yii\filters\ContentNegotiator;
use yii\filters\Cors;
use yii\web\Response;
use app\common\services\ApiResponse;

/**
 * Базовый API контроллер для модуля employer
 * Обеспечивает общую функциональность для всех API endpoints
 */
class BaseApiController extends Controller
{
    /**
     * @var bool Отключаем CSRF валидацию для API
     */
    public $enableCsrfValidation = false;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        // Настройка CORS для мобильного приложения
        $behaviors['corsFilter'] = [
            'class' => Cors::class,
            'cors' => [
                'Origin' => ['*'],
                'Access-Control-Request-Method' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
                'Access-Control-Request-Headers' => ['*'],
                'Access-Control-Allow-Credentials' => false,
                'Access-Control-Max-Age' => 86400,
            ],
        ];

        // Настройка формата ответа
        $behaviors['contentNegotiator'] = [
            'class' => ContentNegotiator::class,
            'formats' => [
                'application/json' => Response::FORMAT_JSON,
            ],
        ];

        return $behaviors;
    }

    /**
     * Отправить успешный ответ
     * 
     * @param mixed $data
     * @param string $message
     * @return Response
     */
    protected function sendSuccess($data = null, $message = '')
    {
        return ApiResponse::success($data, $message)->send();
    }

    /**
     * Отправить ответ с ошибкой
     * 
     * @param string $message
     * @param mixed $data
     * @param int $statusCode
     * @return Response
     */
    protected function sendError($message, $data = null, $statusCode = 400)
    {
        return ApiResponse::error($message, $data, $statusCode)->send();
    }

    /**
     * Отправить ответ "не найдено"
     * 
     * @param string $message
     * @return Response
     */
    protected function sendNotFound($message = 'Resource not found')
    {
        return ApiResponse::notFound($message)->send();
    }

    /**
     * Отправить ответ "не авторизован"
     * 
     * @param string $message
     * @return Response
     */
    protected function sendUnauthorized($message = 'Unauthorized')
    {
        return ApiResponse::unauthorized($message)->send();
    }

    /**
     * Отправить ответ с ошибкой валидации
     * 
     * @param array $errors
     * @return Response
     */
    protected function sendValidationError($errors)
    {
        return ApiResponse::validationError($errors)->send();
    }

    /**
     * Получить данные из запроса
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function getRequestData($key = null, $default = null)
    {
        $request = \Yii::$app->request;
        
        // Сначала проверяем JSON данные
        $rawInput = file_get_contents('php://input');
        if (!empty($rawInput)) {
            $jsonData = json_decode($rawInput, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
                if ($key === null) {
                    return $jsonData;
                }
                if (isset($jsonData[$key])) {
                    return $jsonData[$key];
                }
            }
        }
        
        // Затем проверяем POST данные
        $postData = $request->post();
        if ($key === null && !empty($postData)) {
            return $postData;
        }
        
        if ($key !== null) {
            $postValue = $request->post($key, null);
            if ($postValue !== null) {
                return $postValue;
            }
        }
        
        // Проверяем GET параметры
        $getData = $request->get();
        if ($key === null && !empty($getData)) {
            return $getData;
        }
        
        if ($key !== null) {
            $getValue = $request->get($key, null);
            if ($getValue !== null) {
                return $getValue;
            }
        }
        
        return $key === null ? [] : $default;
    }

    /**
     * Нормализация номера телефона
     * 
     * @param string $phone
     * @return string
     */
    protected function normalizePhone($phone)
    {
        // Удаляем все символы кроме цифр
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Если номер начинается с 998, оставляем как есть
        if (strpos($phone, '998') === 0) {
            return $phone;
        }
        
        // Если номер начинается с 0, заменяем на 998
        if (strpos($phone, '0') === 0) {
            return '998' . substr($phone, 1);
        }
        
        // Если номер состоит из 9 цифр, добавляем 998
        if (strlen($phone) === 9) {
            return '998' . $phone;
        }
        
        return $phone;
    }

    /**
     * Получить переводы
     * 
     * @param string $category
     * @param string $message
     * @param array $params
     * @param string|null $language
     * @return string
     */
    protected function t($category, $message, $params = [], $language = null)
    {
        return \Yii::t($category, $message, $params, $language);
    }

    /**
     * Получить текущего авторизованного работодателя из заголовка Authorization
     * 
     * @return \app\modules\employer\models\Employer|null
     */
    protected function getCurrentEmployer()
    {
        $authHeader = \Yii::$app->request->getHeaders()->get('Authorization');
        
        if (!$authHeader) {
            return null;
        }
        
        // Ожидаем формат "Bearer TOKEN"
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return null;
        }
        
        $token = $matches[1];
        
        return \app\modules\employer\models\Employer::findByAuthToken($token);
    }

    /**
     * Проверка авторизации
     * Бросает исключение если работодатель не авторизован
     * 
     * @return \app\modules\employer\models\Employer
     * @throws \yii\web\UnauthorizedHttpException
     */
    protected function requireAuth()
    {
        $employer = $this->getCurrentEmployer();
        
        if (!$employer) {
            throw new \yii\web\UnauthorizedHttpException($this->t('app', 'Authentication required'));
        }
        
        return $employer;
    }
}
