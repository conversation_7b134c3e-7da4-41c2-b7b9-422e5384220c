a:14:{s:6:"config";s:1743:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:3161:"a:1:{s:8:"messages";a:14:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753274338.676275;i:4;a:0:{}i:5;i:2611760;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753274338.68116;i:4;a:0:{}i:5;i:2790120;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753274338.681193;i:4;a:0:{}i:5;i:2790920;}i:3;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1753274338.710708;i:4;a:0:{}i:5;i:3733528;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753274338.72408;i:4;a:0:{}i:5;i:4181256;}i:5;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753274338.740078;i:4;a:0:{}i:5;i:4686832;}i:6;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753274338.741508;i:4;a:0:{}i:5;i:4711664;}i:90;a:6:{i:0;s:40:"Route requested: '/employer/worker/list'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1753274338.754075;i:4;a:0:{}i:5;i:5013424;}i:91;a:6:{i:0;s:24:"Loading module: employer";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753274338.754098;i:4;a:0:{}i:5;i:5015064;}i:92;a:6:{i:0;s:922:"yii\base\InvalidRouteException: Unable to resolve the request: employer/worker/list in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Controller.php:149
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction()
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction()
#2 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest()
#3 D:\OSPanel\domains\ish_top\web\index.php(12): yii\base\Application->run()
#4 {main}

Next yii\web\NotFoundHttpException: Page not found. in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest()
#1 D:\OSPanel\domains\ish_top\web\index.php(12): yii\base\Application->run()
#2 {main}";i:1;i:1;i:2;s:25:"yii\web\HttpException:404";i:3;d:1753274338.775747;i:4;a:0:{}i:5;i:5105704;}i:93;a:6:{i:0;s:24:"Route to run: site/error";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1753274338.779287;i:4;a:0:{}i:5;i:5207664;}i:94;a:6:{i:0;s:85:"Running action: yii\web\ErrorAction::run(), invoked by app\controllers\SiteController";i:1;i:8;i:2;s:30:"yii\base\Action::runWithParams";i:3;d:1753274338.784941;i:4;a:0:{}i:5;i:5286848;}i:95;a:6:{i:0;s:68:"Rendering view file: D:\OSPanel\domains\ish_top\views\site\error.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1753274338.790956;i:4;a:0:{}i:5;i:5421328;}i:96;a:6:{i:0;s:70:"Rendering view file: D:\OSPanel\domains\ish_top\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1753274338.803626;i:4;a:0:{}i:5;i:5690096;}}}";s:9:"profiling";s:82:"a:3:{s:6:"memory";i:6668016;s:4:"time";d:0.2193739414215088;s:8:"messages";a:0:{}}";s:2:"db";s:27:"a:1:{s:8:"messages";a:0:{}}";s:5:"event";s:4342:"a:25:{i:0;a:5:{s:4:"time";d:1753274338.74706;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1753274338.779836;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1753274338.784898;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"app\controllers\SiteController";}i:3;a:5:{s:4:"time";d:1753274338.790942;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:4;a:5:{s:4:"time";d:1753274338.803037;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:5;a:5:{s:4:"time";d:1753274338.803609;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:6;a:5:{s:4:"time";d:1753274338.816818;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:7;a:5:{s:4:"time";d:1753274338.818977;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:8;a:5:{s:4:"time";d:1753274338.825507;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:9;a:5:{s:4:"time";d:1753274338.834635;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:10;a:5:{s:4:"time";d:1753274338.836157;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:11;a:5:{s:4:"time";d:1753274338.837279;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:12;a:5:{s:4:"time";d:1753274338.837315;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:13;a:5:{s:4:"time";d:1753274338.838103;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:14;a:5:{s:4:"time";d:1753274338.838916;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"app\widgets\Alert";}i:15;a:5:{s:4:"time";d:1753274338.838941;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"app\widgets\Alert";}i:16;a:5:{s:4:"time";d:1753274338.839006;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"app\widgets\Alert";}i:17;a:5:{s:4:"time";d:1753274338.848375;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:18;a:5:{s:4:"time";d:1753274338.848651;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:19;a:5:{s:4:"time";d:1753274338.849228;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:20;a:5:{s:4:"time";d:1753274338.849255;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"app\controllers\SiteController";}i:21;a:5:{s:4:"time";d:1753274338.849269;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:22;a:5:{s:4:"time";d:1753274338.849292;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:23;a:5:{s:4:"time";d:1753274338.851354;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:24;a:5:{s:4:"time";d:1753274338.85183;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1753274338.646043;s:3:"end";d:1753274338.865627;s:6:"memory";i:6668016;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:16569:"a:3:{s:8:"messages";a:83:{i:7;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.74849;i:4;a:0:{}i:5;i:4940144;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.748528;i:4;a:0:{}i:5;i:4940896;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.748582;i:4;a:0:{}i:5;i:4941968;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.748601;i:4;a:0:{}i:5;i:4942720;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.748616;i:4;a:0:{}i:5;i:4943472;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.74872;i:4;a:0:{}i:5;i:4944224;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.748871;i:4;a:0:{}i:5;i:4944976;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.748963;i:4;a:0:{}i:5;i:4945728;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749054;i:4;a:0:{}i:5;i:4946536;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:22:"POST worker/auth/login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749077;i:4;a:0:{}i:5;i:4947336;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:23:"POST worker/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749087;i:4;a:0:{}i:5;i:4948776;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET worker/auth/verify";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749144;i:4;a:0:{}i:5;i:4949576;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:24:"POST worker/auth/refresh";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749156;i:4;a:0:{}i:5;i:4950384;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET worker/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749209;i:4;a:0:{}i:5;i:4951184;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET worker/vacancy/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749349;i:4;a:0:{}i:5;i:4951992;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET worker/vacancy/detail/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749428;i:4;a:0:{}i:5;i:4952808;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET worker/profile/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749466;i:4;a:0:{}i:5;i:4953616;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT worker/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749477;i:4;a:0:{}i:5;i:4954424;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST worker/profile/upload-audio";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749487;i:4;a:0:{}i:5;i:4955240;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749497;i:4;a:0:{}i:5;i:4956048;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/auth/verify-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.74951;i:4;a:0:{}i:5;i:4956856;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:40:"POST employer/auth/complete-registration";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749522;i:4;a:0:{}i:5;i:4957688;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/auth/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749562;i:4;a:0:{}i:5;i:4958496;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:25:"POST employer/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749572;i:4;a:0:{}i:5;i:4959304;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/worker/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749613;i:4;a:0:{}i:5;i:4960112;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.74967;i:4;a:0:{}i:5;i:4960920;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749722;i:4;a:0:{}i:5;i:4963008;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/worker/professions";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749773;i:4;a:0:{}i:5;i:4963816;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:33:"GET employer/worker/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749825;i:4;a:0:{}i:5;i:4964632;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/worker/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749881;i:4;a:0:{}i:5;i:4965440;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/worker/check-access";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749932;i:4;a:0:{}i:5;i:4966256;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/worker/unlock-contact";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749982;i:4;a:0:{}i:5;i:4967072;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST employer/favorite/add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.749997;i:4;a:0:{}i:5;i:4967880;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.75001;i:4;a:0:{}i:5;i:4968688;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/favorite/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750056;i:4;a:0:{}i:5;i:4969496;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/toggle";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750072;i:4;a:0:{}i:5;i:4970304;}i:43;a:6:{i:0;a:3:{s:4:"rule";s:31:"POST employer/favorite/bulk-add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750085;i:4;a:0:{}i:5;i:4971112;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:34:"POST employer/favorite/bulk-remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750098;i:4;a:0:{}i:5;i:4971928;}i:45;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/favorite/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750149;i:4;a:0:{}i:5;i:4972744;}i:46;a:6:{i:0;a:3:{s:4:"rule";s:35:"GET employer/favorite/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750201;i:4;a:0:{}i:5;i:4973560;}i:47;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/profile/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750255;i:4;a:0:{}i:5;i:4974368;}i:48;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.75027;i:4;a:0:{}i:5;i:4975176;}i:49;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750283;i:4;a:0:{}i:5;i:4975984;}i:50;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/profile/change-language";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750296;i:4;a:0:{}i:5;i:4976800;}i:51;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/profile/languages";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750344;i:4;a:0:{}i:5;i:4977608;}i:52;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.75036;i:4;a:0:{}i:5;i:4978416;}i:53;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750407;i:4;a:0:{}i:5;i:4979224;}i:54;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750454;i:4;a:0:{}i:5;i:4980032;}i:55;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/create";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750468;i:4;a:0:{}i:5;i:4980840;}i:56;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/vacancy/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750481;i:4;a:0:{}i:5;i:4981648;}i:57;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750494;i:4;a:0:{}i:5;i:4982456;}i:58;a:6:{i:0;a:3:{s:4:"rule";s:30:"DELETE employer/vacancy/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750506;i:4;a:0:{}i:5;i:4983264;}i:59;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.75052;i:4;a:0:{}i:5;i:4984072;}i:60;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/vacancy/statuses";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750568;i:4;a:0:{}i:5;i:4984880;}i:61;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/access/filter-page";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750616;i:4;a:0:{}i:5;i:4985688;}i:62;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/access/calculate-price";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.75063;i:4;a:0:{}i:5;i:4986504;}i:63;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/access/purchase";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750644;i:4;a:0:{}i:5;i:4987312;}i:64;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/access/my-purchases";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750698;i:4;a:0:{}i:5;i:4988128;}i:65;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/access/available";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750748;i:4;a:0:{}i:5;i:4994568;}i:66;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/access/unlock-contact";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.750797;i:4;a:0:{}i:5;i:4995384;}i:67;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/access/contact-status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753059;i:4;a:0:{}i:5;i:4996200;}i:68;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/access/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753206;i:4;a:0:{}i:5;i:4997008;}i:69;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/tariff/plans";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753255;i:4;a:0:{}i:5;i:4997816;}i:70;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/tariff/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753283;i:4;a:0:{}i:5;i:4998624;}i:71;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET employer/tariff/compare";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753308;i:4;a:0:{}i:5;i:4999432;}i:72;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/tariff/filtered";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753334;i:4;a:0:{}i:5;i:5000240;}i:73;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/tariff/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753363;i:4;a:0:{}i:5;i:5001048;}i:74;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/tariff/recommended";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753409;i:4;a:0:{}i:5;i:5001856;}i:75;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/tariff/discounts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753464;i:4;a:0:{}i:5;i:5002664;}i:76;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/payment/methods";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753589;i:4;a:0:{}i:5;i:5003472;}i:77;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET employer/payment/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753632;i:4;a:0:{}i:5;i:5004280;}i:78;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/payment/history";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753665;i:4;a:0:{}i:5;i:5005088;}i:79;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/payment/callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753676;i:4;a:0:{}i:5;i:5005896;}i:80;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/payment/click-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753689;i:4;a:0:{}i:5;i:5006712;}i:81;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/payment/payme-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753702;i:4;a:0:{}i:5;i:5007528;}i:82;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/payment/uzcard-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753716;i:4;a:0:{}i:5;i:5008344;}i:83;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/payment/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753764;i:4;a:0:{}i:5;i:5009152;}i:84;a:6:{i:0;a:3:{s:4:"rule";s:27:"POST employer/payment/retry";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753781;i:4;a:0:{}i:5;i:5009960;}i:85;a:6:{i:0;a:3:{s:4:"rule";s:21:"employer/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753854;i:4;a:0:{}i:5;i:5010712;}i:86;a:6:{i:0;a:3:{s:4:"rule";s:29:"employer/vacancy/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.75392;i:4;a:0:{}i:5;i:5011464;}i:87;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.753983;i:4;a:0:{}i:5;i:5012216;}i:88;a:6:{i:0;a:3:{s:4:"rule";s:12:"<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.754043;i:4;a:0:{}i:5;i:5012968;}i:89;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753274338.754054;i:4;a:0:{}i:5;i:5013344;}}s:5:"route";s:10:"site/error";s:6:"action";s:26:"yii\web\ErrorAction::run()";}";s:7:"request";s:3246:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:404;s:14:"requestHeaders";a:8:{s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"ea9f3bfb-51f3-4e07-8d8d-1a9034fd6fc4";s:4:"host";s:7:"vacanct";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:6:"cookie";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:15:"responseHeaders";a:7:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6880d7e2b12cd";s:16:"X-Debug-Duration";s:3:"206";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6880d7e2b12cd";}s:5:"route";s:10:"site/error";s:6:"action";s:26:"yii\web\ErrorAction::run()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:38:{s:15:"REDIRECT_STATUS";s:3:"200";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"ea9f3bfb-51f3-4e07-8d8d-1a9034fd6fc4";s:9:"HTTP_HOST";s:7:"vacanct";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:11:"HTTP_COOKIE";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:7:"vacanct";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:40:"D:/OSPanel/domains/ish_top/web/index.php";s:11:"REMOTE_PORT";s:5:"61008";s:12:"REDIRECT_URL";s:21:"/employer/worker/list";s:21:"REDIRECT_QUERY_STRING";s:15:"page=1&limit=10";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:15:"page=1&limit=10";s:11:"REQUEST_URI";s:38:"//employer/worker/list?page=1&limit=10";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1753274338.595514;s:12:"REQUEST_TIME";i:1753274338;}s:3:"GET";a:2:{s:4:"page";s:1:"1";s:5:"limit";s:2:"10";}s:4:"POST";a:0:{}s:6:"COOKIE";a:1:{s:9:"PHPSESSID";s:32:"gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:1940:"a:5:{s:19:"app\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:30:"D:/OSPanel/domains/ish_top/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:53:"D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2/assets";s:8:"basePath";s:46:"D:\OSPanel\domains\ish_top\web\assets\19864d32";s:7:"baseUrl";s:16:"/assets/19864d32";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:57:"D:\OSPanel\domains\ish_top\vendor/bower-asset/jquery/dist";s:8:"basePath";s:46:"D:\OSPanel\domains\ish_top\web\assets\a68cfe0e";s:7:"baseUrl";s:16:"/assets/a68cfe0e";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:60:"D:\OSPanel\domains\ish_top\vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:46:"D:\OSPanel\domains\ish_top\web\assets\4f767234";s:7:"baseUrl";s:16:"/assets/4f767234";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:60:"D:\OSPanel\domains\ish_top\vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:46:"D:\OSPanel\domains\ish_top\web\assets\4f767234";s:7:"baseUrl";s:16:"/assets/4f767234";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"6880d7e2b12cd";s:3:"url";s:52:"http://vacanct//employer/worker/list?page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753274338.595514;s:10:"statusCode";i:404;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:6668016;s:14:"processingTime";d:0.2193739414215088;}s:10:"exceptions";a:0:{}}