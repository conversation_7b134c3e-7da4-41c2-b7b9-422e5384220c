a:10:{s:6:"config";s:1745:"a:5:{s:10:"phpVersion";s:6:"8.1.31";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"8.1.31";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:2535:"a:1:{s:8:"messages";a:7:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753273878.965503;i:4;a:0:{}i:5;i:2327528;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753273878.966571;i:4;a:0:{}i:5;i:2437392;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753273878.966582;i:4;a:0:{}i:5;i:2437816;}i:3;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753273878.966876;i:4;a:0:{}i:5;i:2464168;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753273878.966883;i:4;a:0:{}i:5;i:2465344;}i:5;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753273878.970863;i:4;a:0:{}i:5;i:2975560;}i:6;a:6:{i:0;s:1438:"yii\base\InvalidRouteException: Unable to resolve the request: migrate/status in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Controller.php:149
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction('status', Array)
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction('status', Array)
#2 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction('migrate/status', Array)
#3 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction('migrate/status', Array)
#4 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest(Object(yii\console\Request))
#5 D:\OSPanel\domains\ish_top\yii(20): yii\base\Application->run()
#6 {main}

Next yii\console\UnknownCommandException: Unknown command "migrate/status". in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction('migrate/status', Array)
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest(Object(yii\console\Request))
#2 D:\OSPanel\domains\ish_top\yii(20): yii\base\Application->run()
#3 {main}";i:1;i:1;i:2;s:35:"yii\console\UnknownCommandException";i:3;d:1753273879.047208;i:4;a:0:{}i:5;i:3664944;}}}";s:9:"profiling";s:82:"a:3:{s:6:"memory";i:5770192;s:4:"time";d:0.6782810688018799;s:8:"messages";a:0:{}}";s:2:"db";s:27:"a:1:{s:8:"messages";a:0:{}}";s:5:"event";s:186:"a:1:{i:0;a:5:{s:4:"time";d:1753273878.972856;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\console\Application";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1753273878.961191;s:3:"end";d:1753273879.639484;s:6:"memory";i:5770192;}";s:4:"dump";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6880d616ed1ab";s:3:"url";s:22:"php yii migrate/status";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1753273878.951654;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:5770192;s:14:"processingTime";d:0.6782810688018799;}s:10:"exceptions";a:0:{}}