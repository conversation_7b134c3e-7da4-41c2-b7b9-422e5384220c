a:14:{s:6:"config";s:1743:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:14743:"a:1:{s:8:"messages";a:21:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753275162.364826;i:4;a:0:{}i:5;i:2611920;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753275162.365614;i:4;a:0:{}i:5;i:2790280;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753275162.365624;i:4;a:0:{}i:5;i:2791080;}i:3;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1753275162.370237;i:4;a:0:{}i:5;i:3733688;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753275162.372505;i:4;a:0:{}i:5;i:4181416;}i:5;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753275162.37478;i:4;a:0:{}i:5;i:4686992;}i:6;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753275162.374965;i:4;a:0:{}i:5;i:4711824;}i:34;a:6:{i:0;s:40:"Route requested: 'employer/worker/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1753275162.375964;i:4;a:0:{}i:5;i:4962896;}i:35;a:6:{i:0;s:24:"Loading module: employer";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753275162.375968;i:4;a:0:{}i:5;i:4964536;}i:36;a:6:{i:0;s:35:"Route to run: employer/worker/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1753275162.378384;i:4;a:0:{}i:5;i:5294856;}i:37;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1753275162.379452;i:4;a:0:{}i:5;i:5507504;}i:38;a:6:{i:0;s:80:"Running action: app\modules\employer\controllers\WorkerController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1753275162.379487;i:4;a:0:{}i:5;i:5508776;}i:39;a:6:{i:0;s:96:"SELECT COUNT(*) FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.398582;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7252328;}i:40;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1753275162.398628;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7255784;}i:45;a:6:{i:0;s:129:"SELECT * FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) ORDER BY "w"."created_at" DESC LIMIT 20";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.436493;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7262152;}i:48;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'workers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.437769;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7306800;}i:51;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='workers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.449362;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7357720;}i:54;a:6:{i:0;s:54:"SELECT * FROM "worker_professions" WHERE "worker_id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.452051;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7423752;}i:57;a:6:{i:0;s:50:"SELECT * FROM "professions" WHERE "id" IN (16, 17)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.453879;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7431192;}i:60;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'professions'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.454966;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7442664;}i:63;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='professions'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.45895;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7451848;}}}";s:9:"profiling";s:25724:"a:3:{s:6:"memory";i:7884424;s:4:"time";d:0.11549496650695801;s:8:"messages";a:18:{i:41;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1753275162.398638;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7257288;}i:42;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1753275162.433328;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7259584;}i:43;a:6:{i:0;s:96:"SELECT COUNT(*) FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.433356;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7260848;}i:44;a:6:{i:0;s:96:"SELECT COUNT(*) FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.436306;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7262456;}i:46;a:6:{i:0;s:129:"SELECT * FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) ORDER BY "w"."created_at" DESC LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.43651;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7265216;}i:47;a:6:{i:0;s:129:"SELECT * FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) ORDER BY "w"."created_at" DESC LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.43716;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7270080;}i:49;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'workers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.437793;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7308664;}i:50;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'workers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.448552;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7339368;}i:52;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='workers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.449383;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7359584;}i:53;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='workers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.451282;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7361520;}i:55;a:6:{i:0;s:54:"SELECT * FROM "worker_professions" WHERE "worker_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.452067;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7426752;}i:56;a:6:{i:0;s:54:"SELECT * FROM "worker_professions" WHERE "worker_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.453739;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7429632;}i:58;a:6:{i:0;s:50:"SELECT * FROM "professions" WHERE "id" IN (16, 17)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.453895;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7434216;}i:59;a:6:{i:0;s:50:"SELECT * FROM "professions" WHERE "id" IN (16, 17)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.454858;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7437720;}i:61;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'professions'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.454985;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7444528;}i:62;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'professions'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.458488;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7457264;}i:64;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='professions'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.458979;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7453712;}i:65;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='professions'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.461057;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7461280;}}}";s:2:"db";s:24434:"a:1:{s:8:"messages";a:16:{i:43;a:6:{i:0;s:96:"SELECT COUNT(*) FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.433356;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7260848;}i:44;a:6:{i:0;s:96:"SELECT COUNT(*) FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.436306;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7262456;}i:46;a:6:{i:0;s:129:"SELECT * FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) ORDER BY "w"."created_at" DESC LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.43651;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7265216;}i:47;a:6:{i:0;s:129:"SELECT * FROM "workers" "w" WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) ORDER BY "w"."created_at" DESC LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.43716;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7270080;}i:49;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'workers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.437793;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7308664;}i:50;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'workers'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.448552;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7339368;}i:52;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='workers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.449383;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7359584;}i:53;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='workers'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.451282;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7361520;}i:55;a:6:{i:0;s:54:"SELECT * FROM "worker_professions" WHERE "worker_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.452067;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7426752;}i:56;a:6:{i:0;s:54:"SELECT * FROM "worker_professions" WHERE "worker_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.453739;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7429632;}i:58;a:6:{i:0;s:50:"SELECT * FROM "professions" WHERE "id" IN (16, 17)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.453895;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7434216;}i:59;a:6:{i:0;s:50:"SELECT * FROM "professions" WHERE "id" IN (16, 17)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.454858;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7437720;}i:61;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'professions'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.454985;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7444528;}i:62;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'professions'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.458488;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7457264;}i:64;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='professions'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.458979;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7453712;}i:65;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='professions'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753275162.461057;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:110;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:61;s:8:"function";s:14:"getWorkersList";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7461280;}}}";s:5:"event";s:3803:"a:21:{i:0;a:5:{s:4:"time";d:1753275162.375713;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1753275162.378457;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1753275162.378464;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:3;a:5:{s:4:"time";d:1753275162.379479;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\employer\controllers\WorkerController";}i:4;a:5:{s:4:"time";d:1753275162.383778;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:5;a:5:{s:4:"time";d:1753275162.433319;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1753275162.437601;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\worker\models\Worker";}i:7;a:5:{s:4:"time";d:1753275162.4516;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:8;a:5:{s:4:"time";d:1753275162.45161;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:9;a:5:{s:4:"time";d:1753275162.454927;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\worker\models\Profession";}i:10;a:5:{s:4:"time";d:1753275162.461474;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\worker\models\Profession";}i:11;a:5:{s:4:"time";d:1753275162.461498;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\worker\models\Profession";}i:12;a:5:{s:4:"time";d:1753275162.461504;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\modules\worker\models\Profession";}i:13;a:5:{s:4:"time";d:1753275162.46155;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\worker\models\Worker";}i:14;a:5:{s:4:"time";d:1753275162.462589;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\employer\controllers\WorkerController";}i:15;a:5:{s:4:"time";d:1753275162.462944;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:16;a:5:{s:4:"time";d:1753275162.462951;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:17;a:5:{s:4:"time";d:1753275162.462958;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:18;a:5:{s:4:"time";d:1753275162.462964;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:19;a:5:{s:4:"time";d:1753275162.469982;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:20;a:5:{s:4:"time";d:1753275162.470086;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1753275162.359262;s:3:"end";d:1753275162.475275;s:6:"memory";i:7884424;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:5426:"a:3:{s:8:"messages";a:27:{i:7;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375888;i:4;a:0:{}i:5;i:4940304;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375895;i:4;a:0:{}i:5;i:4941056;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375898;i:4;a:0:{}i:5;i:4942128;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375901;i:4;a:0:{}i:5;i:4942880;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375904;i:4;a:0:{}i:5;i:4943632;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375907;i:4;a:0:{}i:5;i:4944384;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375909;i:4;a:0:{}i:5;i:4945136;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375911;i:4;a:0:{}i:5;i:4945888;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375917;i:4;a:0:{}i:5;i:4946696;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:22:"POST worker/auth/login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375919;i:4;a:0:{}i:5;i:4947496;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:23:"POST worker/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375922;i:4;a:0:{}i:5;i:4948936;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET worker/auth/verify";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375925;i:4;a:0:{}i:5;i:4949736;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:24:"POST worker/auth/refresh";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375927;i:4;a:0:{}i:5;i:4950544;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET worker/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.37593;i:4;a:0:{}i:5;i:4951344;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET worker/vacancy/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375932;i:4;a:0:{}i:5;i:4952152;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET worker/vacancy/detail/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375935;i:4;a:0:{}i:5;i:4952968;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET worker/profile/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375937;i:4;a:0:{}i:5;i:4953776;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT worker/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.37594;i:4;a:0:{}i:5;i:4954584;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST worker/profile/upload-audio";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375941;i:4;a:0:{}i:5;i:4955400;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375943;i:4;a:0:{}i:5;i:4956208;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/auth/verify-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375945;i:4;a:0:{}i:5;i:4957016;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:40:"POST employer/auth/complete-registration";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375947;i:4;a:0:{}i:5;i:4957848;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/auth/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.37595;i:4;a:0:{}i:5;i:4958656;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:25:"POST employer/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375951;i:4;a:0:{}i:5;i:4959464;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/worker/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.375954;i:4;a:0:{}i:5;i:4960272;}i:32;a:6:{i:0;s:52:"Request parsed with URL rule: employer/worker/search";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1753275162.375958;i:4;a:0:{}i:5;i:4961560;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/search";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753275162.37596;i:4;a:0:{}i:5;i:4963192;}}s:5:"route";s:21:"employer/worker/index";s:6:"action";s:64:"app\modules\employer\controllers\WorkerController::actionIndex()";}";s:7:"request";s:3417:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:8:{s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"946da2db-e3d9-4577-aa02-d7df83be5839";s:4:"host";s:7:"vacanct";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:6:"cookie";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:32:"Access-Control-Allow-Credentials";s:5:"false";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6880db1a5b056";s:16:"X-Debug-Duration";s:3:"112";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6880db1a5b056";}s:5:"route";s:21:"employer/worker/index";s:6:"action";s:64:"app\modules\employer\controllers\WorkerController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:38:{s:15:"REDIRECT_STATUS";s:3:"200";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"946da2db-e3d9-4577-aa02-d7df83be5839";s:9:"HTTP_HOST";s:7:"vacanct";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:11:"HTTP_COOKIE";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:7:"vacanct";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:40:"D:/OSPanel/domains/ish_top/web/index.php";s:11:"REMOTE_PORT";s:5:"61338";s:12:"REDIRECT_URL";s:23:"/employer/worker/search";s:21:"REDIRECT_QUERY_STRING";s:27:"query=ererf&page=1&limit=10";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:27:"query=ererf&page=1&limit=10";s:11:"REQUEST_URI";s:51:"/employer/worker/search?query=ererf&page=1&limit=10";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1753275162.352588;s:12:"REQUEST_TIME";i:1753275162;}s:3:"GET";a:3:{s:5:"query";s:5:"ererf";s:4:"page";s:1:"1";s:5:"limit";s:2:"10";}s:4:"POST";a:0:{}s:6:"COOKIE";a:1:{s:9:"PHPSESSID";s:32:"gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6880db1a5b056";s:3:"url";s:65:"http://vacanct/employer/worker/search?query=ererf&page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753275162.352588;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7884424;s:14:"processingTime";d:0.11549496650695801;}s:10:"exceptions";a:0:{}}