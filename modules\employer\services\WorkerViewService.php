<?php

namespace app\modules\employer\services;

use app\modules\worker\models\Worker;
use app\modules\employer\models\PurchaseWorker;
use yii\db\Query;
use yii\data\Pagination;

/**
 * Сервис для просмотра работников работодателями
 */
class WorkerViewService
{
    /**
     * Поиск работников по поисковому запросу
     * 
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @return array
     */
   public function searchWorkers($filters = [], $page = 1, $limit = 20)
    {
        $query = (new Query())
            ->select(['w.*'])
            ->from(['w' => 'workers'])
            ->innerJoin(['wp' => 'worker_professions'], 'w.id = wp.worker_id')
            ->innerJoin(['p' => 'professions'], 'wp.profession_id = p.id')
            ->where(['w.deleted_at' => null])
            ->andWhere(['w.profile_status' => Worker::PROFILE_STATUS_COMPLETE]);

        // Поиск по профессии
        $searchTerm = $filters['search'] ?? $filters['query'] ?? null;
        if (!empty($searchTerm)) {
            $search = '%' . mb_strtolower($searchTerm) . '%';
            // Учитываем название профессии, имя работника и его описание
            $query->andWhere([
                'or',
                ['ilike', 'p.name_uz', $search],
                ['ilike', 'p.name_ru', $search],
                ['ilike', 'p.name_en', $search],
                ['ilike', 'w.name', $search],
                ['ilike', 'w.about', $search]
            ]);

            // Добавляем релевантность (приоритет: точное совпадение имени работника > точное совпадение профессии > частичное совпадение)
            $query->addSelect([
                new \yii\db\Expression("
                    CASE 
                        WHEN LOWER(w.name) = :exact THEN 4
                        WHEN LOWER(p.name_uz) = :exact THEN 3
                        WHEN LOWER(p.name_ru) = :exact THEN 3
                        WHEN LOWER(p.name_en) = :exact THEN 3
                        WHEN LOWER(w.name) ILIKE :like THEN 2
                        WHEN LOWER(p.name_uz) ILIKE :like THEN 2
                        WHEN LOWER(p.name_ru) ILIKE :like THEN 2
                        WHEN LOWER(p.name_en) ILIKE :like THEN 2
                        ELSE 1
                    END AS relevance_score
                ", [
                    ':exact' => mb_strtolower($searchTerm),
                    ':like' => '%' . mb_strtolower($searchTerm) . '%'
                ])
            ]);

            // Избегаем дубликатов работников, если они имеют несколько профессий
            $query->distinct();
        }

        // Фильтры
        if (!empty($filters['profession_ids'])) {
            $professionIds = (array)$filters['profession_ids'];
            $query->andWhere(['p.id' => $professionIds]);
        }

        // Фильтры по возрасту применяем, только если параметры заданы
        if (isset($filters['min_age']) && $filters['min_age'] !== '') {
            $query->andWhere(['>=', 'w.age', (int)$filters['min_age']]);
        }

        if (isset($filters['max_age']) && $filters['max_age'] !== '') {
            $query->andWhere(['<=', 'w.age', (int)$filters['max_age']]);
        }

        // Фильтры по опыту работы применяем, только если параметры заданы
        if (isset($filters['min_experience']) && $filters['min_experience'] !== '') {
            $query->andWhere(['>=', 'w.experience_years', (int)$filters['min_experience']]);
        }

        if (isset($filters['max_experience']) && $filters['max_experience'] !== '') {
            $query->andWhere(['<=', 'w.experience_years', (int)$filters['max_experience']]);
        }

        // Координаты
        if (!empty($filters['lat']) && !empty($filters['lng']) && !empty($filters['radius'])) {
            $lat = (float)$filters['lat'];
            $lng = (float)$filters['lng'];
            $radius = (float)$filters['radius'];

            $query->andWhere([
                'and',
                ['not', ['w.lat' => null]],
                ['not', ['w.long' => null]],
                new \yii\db\Expression(
                    "(6371 * acos(cos(radians(:lat)) * cos(radians(w.lat)) * cos(radians(w.long) - radians(:lng)) + sin(radians(:lat)) * sin(radians(w.lat)))) <= :radius",
                    [':lat' => $lat, ':lng' => $lng, ':radius' => $radius]
                )
            ]);
        }

        // Сортировка
        if (!empty($searchTerm)) {
            $query->orderBy('relevance_score DESC, w.created_at DESC');
        } else {
            $query->orderBy('w.created_at DESC');
        }

        // Логирование
        \Yii::info('Search Query SQL: ' . $query->createCommand()->getRawSql(), 'search-debug');

        // Пагинация
        $totalCount = $query->count();
        $offset = ($page - 1) * $limit;

        $workerRows = $query->offset($offset)->limit($limit)->all();

        // Отладочная информация
        \Yii::info('Search results count: ' . count($workerRows), 'search-debug');
        \Yii::info('Search results: ' . json_encode($workerRows), 'search-debug');

        // Загружаем данные Worker с профессиями
        $workerIds = array_column($workerRows, 'id');
        \Yii::info('Worker IDs: ' . json_encode($workerIds), 'search-debug');

        $workers = Worker::find()
            ->with(['professions'])
            ->where(['id' => $workerIds])
            ->indexBy('id')
            ->all();

        \Yii::info('Loaded workers count: ' . count($workers), 'search-debug');

        // Форматируем
        $formattedWorkers = [];
        $employerId = $filters['employer_id'] ?? null;

        foreach ($workerRows as $row) {
            \Yii::info('Processing row: ' . json_encode($row), 'search-debug');
            if (isset($workers[$row['id']])) {
                $formattedWorker = $this->formatWorkerForList($workers[$row['id']], $employerId);
                $formattedWorkers[] = $formattedWorker;
                \Yii::info('Formatted worker: ' . json_encode($formattedWorker), 'search-debug');
            } else {
                \Yii::info('Worker not found in loaded workers: ' . $row['id'], 'search-debug');
            }
        }

        \Yii::info('Final formatted workers count: ' . count($formattedWorkers), 'search-debug');

        return [
            'workers' => $formattedWorkers,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total_count' => $totalCount,
                'total_pages' => ceil($totalCount / $limit),
            ]
        ];
    }

    /**
     * Получить список работников с фильтрацией и пагинацией
     * 
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getWorkersList($filters = [], $page = 1, $limit = 20)
    {
        $query = Worker::find()
            ->alias('w')
            ->with(['professions'])
            ->where(['w.deleted_at' => null])
            ->andWhere(['w.profile_status' => Worker::PROFILE_STATUS_COMPLETE]);

        // Фильтр по профессии
        if (!empty($filters['profession_ids'])) {
            $professionIds = is_array($filters['profession_ids']) ? $filters['profession_ids'] : [$filters['profession_ids']];
            $query->joinWith('professions wp')
                  ->andWhere(['wp.profession_id' => $professionIds]);
        }

        // Фильтр по возрасту
        if (!empty($filters['min_age'])) {
            $query->andWhere(['>=', 'w.age', (int)$filters['min_age']]);
        }
        if (!empty($filters['max_age'])) {
            $query->andWhere(['<=', 'w.age', (int)$filters['max_age']]);
        }

        // Фильтр по опыту работы
        if (!empty($filters['min_experience'])) {
            $query->andWhere(['>=', 'w.experience_years', (int)$filters['min_experience']]);
        }
        if (!empty($filters['max_experience'])) {
            $query->andWhere(['<=', 'w.experience_years', (int)$filters['max_experience']]);
        }

        // Фильтр по координатам (радиус в км)
        if (!empty($filters['lat']) && !empty($filters['lng']) && !empty($filters['radius'])) {
            $lat = (float)$filters['lat'];
            $lng = (float)$filters['lng'];
            $radius = (float)$filters['radius'];
            
            // Используем формулу Haversine для расчета расстояния
            $query->andWhere([
                'and',
                ['not', ['w.lat' => null]],
                ['not', ['w.long' => null]],
                new \yii\db\Expression(
                    "(6371 * acos(cos(radians(:lat)) * cos(radians(w.lat)) * cos(radians(w.long) - radians(:lng)) + sin(radians(:lat)) * sin(radians(w.lat)))) <= :radius",
                    [':lat' => $lat, ':lng' => $lng, ':radius' => $radius]
                )
            ]);
        }



        // Сортировка
        $orderBy = 'w.created_at DESC'; // По умолчанию сортируем по дате создания
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'name_asc':
                    $orderBy = 'w.name ASC';
                    break;
                case 'name_desc':
                    $orderBy = 'w.name DESC';
                    break;
                case 'age_asc':
                    $orderBy = 'w.age ASC';
                    break;
                case 'age_desc':
                    $orderBy = 'w.age DESC';
                    break;
                case 'experience_asc':
                    $orderBy = 'w.experience_years ASC';
                    break;
                case 'experience_desc':
                    $orderBy = 'w.experience_years DESC';
                    break;
            }
        }
        $query->orderBy($orderBy);

        // Подсчет общего количества
        $totalCount = $query->count();

        // Пагинация
        $offset = ($page - 1) * $limit;
        $workers = $query->offset($offset)->limit($limit)->all();

        // Форматирование данных
        $formattedWorkers = [];
        $employerId = isset($filters['employer_id']) ? $filters['employer_id'] : null;
        foreach ($workers as $worker) {
            $formattedWorkers[] = $this->formatWorkerForList($worker, $employerId);
        }

        return [
            'workers' => $formattedWorkers,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total_count' => $totalCount,
                'total_pages' => ceil($totalCount / $limit),
            ],
            'filters_applied' => $filters
        ];
    }

    /**
     * Получить детали работника (требует авторизации)
     *
     * @param int $workerId
     * @param int|null $employerId для проверки избранного
     * @return array|null
     */
    public function getWorkerDetails($workerId, $employerId = null)
    {
        $worker = Worker::find()
            ->with(['professions'])
            ->where([
                'id' => $workerId,
                'deleted_at' => null,
                'profile_status' => Worker::PROFILE_STATUS_COMPLETE
            ])
            ->one();

        if (!$worker) {
            return null;
        }

        $details = $this->formatWorkerForDetails($worker, $employerId);

        // Проверяем, находится ли работник в избранных у работодателя
        if ($employerId) {
            $details['is_favorite'] = $this->isWorkerInFavorites($workerId, $employerId);
        }

        return $details;
    }

    /**
     * Получить работников по профессии
     * 
     * @param array $professionIds
     * @param int $limit
     * @return array
     */
    public function getWorkersByProfession($professionIds, $limit = 10)
    {
        $workers = Worker::find()
            ->alias('w')
            ->with(['professions'])
            ->joinWith('professions wp')
            ->where([
                'w.deleted_at' => null,
                'w.profile_status' => Worker::PROFILE_STATUS_COMPLETE,
                'wp.profession_id' => $professionIds
            ])
            ->limit($limit)
            ->all();

        $formattedWorkers = [];
        foreach ($workers as $worker) {
            $formattedWorkers[] = $this->formatWorkerForList($worker);
        }

        return $formattedWorkers;
    }

    /**
     * Получить список профессий для фильтрации
     * 
     * @return array
     */
    public function getProfessionsForFilter()
    {
        return (new Query())
            ->select(['p.id', 'p.name_uz', 'p.name_ru', 'p.name_en'])
            ->from('{{%professions}} p')
            ->innerJoin('{{%worker_professions}} wp', 'wp.profession_id = p.id')
            ->innerJoin('{{%workers}} w', 'w.id = wp.worker_id AND w.deleted_at IS NULL AND w.profile_status = ' . Worker::PROFILE_STATUS_COMPLETE)
            ->where(['p.deleted_at' => null])
            ->groupBy(['p.id', 'p.name_uz', 'p.name_ru', 'p.name_en'])
            ->orderBy('p.name_uz ASC')
            ->all();
    }

    /**
     * Проверить, находится ли работник в избранных у работодателя
     * 
     * @param int $workerId
     * @param int $employerId
     * @return bool
     */
    private function isWorkerInFavorites($workerId, $employerId)
    {
        return (new Query())
            ->from('{{%employer_favorites}}')
            ->where([
                'employer_id' => $employerId,
                'worker_id' => $workerId,
                'deleted_at' => null
            ])
            ->exists();
    }

    /**
     * Форматирование работника для списка (краткая информация)
     *
     * @param Worker $worker
     * @param int|null $employerId
     * @return array
     */
    private function formatWorkerForList($worker, $employerId = null)
    {
        $data = [
            'id' => $worker->id,
            'name' => $worker->name,
            'age' => $worker->age,
            'experience_years' => $worker->experience_years,
            'professions' => isset($worker->professions) ? array_map(function($profession) {
                return [
                    'id' => $profession->id,
                    'name_uz' => $profession->name_uz,
                    'name_ru' => $profession->name_ru,
                    'name_en' => $profession->name_en,
                ];
            }, $worker->professions) : [],
            'location' => [
                'lat' => $worker->lat,
                'lng' => $worker->long,
            ],
            'created_at' => $worker->created_at,
        ];

        // Добавляем информацию о доступе к телефону
        if ($employerId) {
            $hasAccess = $this->hasPhoneAccess($employerId, $worker->id);
            $data['has_phone_access'] = $hasAccess;

            if ($hasAccess) {
                $data['phone'] = $worker->phone;
            }
        }

        return $data;
    }

    /**
     * Форматирование работника для детального просмотра
     *
     * @param Worker $worker
     * @param int|null $employerId
     * @return array
     */
    private function formatWorkerForDetails($worker, $employerId = null)
    {
        $data = [
            'id' => $worker->id,
            'name' => $worker->name,
            'age' => $worker->age,
            'experience_years' => $worker->experience_years,
            'about' => $worker->about,
            'audio_file_url' => $worker->audio_file_url,
            'professions' => isset($worker->professions) ? array_map(function($profession) {
                return [
                    'id' => $profession->id,
                    'name_uz' => $profession->name_uz,
                    'name_ru' => $profession->name_ru,
                    'name_en' => $profession->name_en,
                ];
            }, $worker->professions) : [],
            'location' => [
                'lat' => $worker->lat,
                'lng' => $worker->long,
            ],
            'language' => $worker->language,
            'created_at' => $worker->created_at,
        ];

        // Проверяем доступ к телефону
        if ($employerId) {
            $hasAccess = $this->hasPhoneAccess($employerId, $worker->id);
            $data['has_phone_access'] = $hasAccess;

            if ($hasAccess) {
                $data['phone'] = $worker->phone;
            } else {
                $data['phone_access_required'] = true;
            }
        } else {
            // Если работодатель не указан, скрываем телефон
            $data['has_phone_access'] = false;
            $data['phone_access_required'] = true;
        }

        return $data;
    }

    /**
     * Получить статистику работников
     * 
     * @return array
     */
    public function getWorkersStatistics()
    {
        $totalWorkers = Worker::find()
            ->where(['deleted_at' => null, 'profile_status' => Worker::PROFILE_STATUS_COMPLETE])
            ->count();

        $avgAge = Worker::find()
            ->where(['deleted_at' => null, 'profile_status' => Worker::PROFILE_STATUS_COMPLETE])
            ->andWhere(['not', ['age' => null]])
            ->average('age');

        $avgExperience = Worker::find()
            ->where(['deleted_at' => null, 'profile_status' => Worker::PROFILE_STATUS_COMPLETE])
            ->andWhere(['not', ['experience_years' => null]])
            ->average('experience_years');

        return [
            'total_workers' => (int)$totalWorkers,
            'average_age' => round($avgAge, 1),
            'average_experience' => round($avgExperience, 1),
        ];
    }

    /**
     * Проверить, есть ли у работодателя доступ к телефону работника
     *
     * @param int $employerId
     * @param int $workerId
     * @return bool
     */
    private function hasPhoneAccess($employerId, $workerId)
    {
        return PurchaseWorker::hasEmployerAccess($employerId, $workerId);
    }
}
