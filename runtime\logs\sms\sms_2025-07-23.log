[2025-07-23 11:59:26] [info] Eskiz.uz authorization successful
[2025-07-23 11:59:26] [info] Starting SMS validation code send to +998930960195 for employer
[2025-07-23 11:59:26] [info] Trying exact approved template text: Tasdiqlash kodi [user_type] hisobiga kirish uchun IshTop platformasida: Kod: [code] Kodni hech kimga bermang!
[2025-07-23 11:59:26] [info] Attempting to send SMS to +998930960195 with message: Tasdiqlash kodi [user_type] hisobiga kirish uchun IshTop platformasida: Kod: [code] Kodni hech kimga bermang!
[2025-07-23 11:59:26] [info] Eskiz.uz API response status: 200 for phone +998930960195
[2025-07-23 11:59:26] [info] Eskiz.uz API response content: {"id":"587f7da2-5007-448d-a2d2-d3f8aac3efec","message":"Waiting for SMS provider","status":"waiting"} for phone +998930960195
[2025-07-23 11:59:26] [info] SMS sent successfully to +998930960195 with response: {"id":"587f7da2-5007-448d-a2d2-d3f8aac3efec","message":"Waiting for SMS provider","status":"waiting"}
[2025-07-23 11:59:58] [info] Eskiz.uz authorization successful
[2025-07-23 11:59:58] [info] Starting SMS validation code send to +998930960195 for employer
[2025-07-23 11:59:58] [info] Using approved template with variables replaced: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!
[2025-07-23 11:59:58] [info] Attempting to send SMS to +998930960195 with message: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!
[2025-07-23 11:59:58] [info] Eskiz.uz API response status: 400 for phone +998930960195
[2025-07-23 11:59:58] [info] Eskiz.uz API response content: {"id":"6215b07c-5705-4e1f-8d4b-eda6d3ce0b88","message":"Этот смс текст еще не прошёл модерацию. Сначала добавьте его через API - Шаблоны - Отправить шаблон или через кабинет my.eskiz.uz - СМС - Мои тексты.","status":"error"} for phone +998930960195
[2025-07-23 11:59:58] [error] SMS sending failed to +998930960195: {"id":"6215b07c-5705-4e1f-8d4b-eda6d3ce0b88","message":"Этот смс текст еще не прошёл модерацию. Сначала добавьте его через API - Шаблоны - Отправить шаблон или через кабинет my.eskiz.uz - СМС - Мои тексты.","status":"error"}
[2025-07-23 12:04:18] [info] Eskiz.uz authorization successful
[2025-07-23 12:04:18] [info] Starting SMS validation code send to +998930960195 for employer
[2025-07-23 12:04:18] [info] Template before replacement: Tasdiqlash kodi [user_type] hisobiga kirish uchun IshTop platformasida: Kod: [code] Kodni hech kimga bermang!
[2025-07-23 12:04:18] [info] Variables: user_type=Ish beruvchi, code=1234
[2025-07-23 12:04:18] [info] Final message after replacement: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!
[2025-07-23 12:04:18] [info] Attempting to send SMS to +998930960195 with message: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!
[2025-07-23 12:04:18] [info] Eskiz.uz API response status: 400 for phone +998930960195
[2025-07-23 12:04:18] [info] Eskiz.uz API response content: {"id":"6d8ad68b-31dd-48d2-ac34-8aa156cc1dd2","message":"Этот смс текст еще не прошёл модерацию. Сначала добавьте его через API - Шаблоны - Отправить шаблон или через кабинет my.eskiz.uz - СМС - Мои тексты.","status":"error"} for phone +998930960195
[2025-07-23 12:04:18] [error] SMS sending failed to +998930960195: {"id":"6d8ad68b-31dd-48d2-ac34-8aa156cc1dd2","message":"Этот смс текст еще не прошёл модерацию. Сначала добавьте его через API - Шаблоны - Отправить шаблон или через кабинет my.eskiz.uz - СМС - Мои тексты.","status":"error"}
[2025-07-23 12:05:11] [info] Eskiz.uz authorization successful
[2025-07-23 12:05:11] [info] Starting SMS validation code send to +998930960195 for employer
[2025-07-23 12:05:11] [info] Using approved template with variables (will be sent as-is): Tasdiqlash kodi [user_type] hisobiga kirish uchun IshTop platformasida: Kod: [code] Kodni hech kimga bermang!
[2025-07-23 12:05:11] [info] Note: Variables user_type=Ish beruvchi, code=1234 will NOT be replaced in SMS
[2025-07-23 12:05:11] [info] Client app should replace variables when displaying to user
[2025-07-23 12:05:11] [info] Attempting to send SMS to +998930960195 with message: Tasdiqlash kodi [user_type] hisobiga kirish uchun IshTop platformasida: Kod: [code] Kodni hech kimga bermang!
[2025-07-23 12:05:11] [info] Eskiz.uz API response status: 200 for phone +998930960195
[2025-07-23 12:05:11] [info] Eskiz.uz API response content: {"id":"e91a4fea-452c-4b75-afbd-64c0ca57e95b","message":"Waiting for SMS provider","status":"waiting"} for phone +998930960195
[2025-07-23 12:05:11] [info] SMS sent successfully to +998930960195 with response: {"id":"e91a4fea-452c-4b75-afbd-64c0ca57e95b","message":"Waiting for SMS provider","status":"waiting"}
[2025-07-23 12:06:24] [info] Eskiz.uz authorization successful
[2025-07-23 12:06:24] [info] Starting SMS validation code send to +998930960195 for employer
[2025-07-23 12:06:24] [info] Using template for employer: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: {code} Kodni hech kimga bermang!
[2025-07-23 12:06:24] [info] Final message after code replacement: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!
[2025-07-23 12:06:24] [info] Attempting to send SMS to +998930960195 with message: Tasdiqlash kodi Ish beruvchi hisobiga kirish uchun IshTop platformasida: Kod: 1234 Kodni hech kimga bermang!
[2025-07-23 12:06:24] [info] Eskiz.uz API response status: 400 for phone +998930960195
[2025-07-23 12:06:24] [info] Eskiz.uz API response content: {"id":"e3fc9615-704a-4760-a516-c1afbf11d7f6","message":"Этот смс текст еще не прошёл модерацию. Сначала добавьте его через API - Шаблоны - Отправить шаблон или через кабинет my.eskiz.uz - СМС - Мои тексты.","status":"error"} for phone +998930960195
[2025-07-23 12:06:24] [error] SMS sending failed to +998930960195: {"id":"e3fc9615-704a-4760-a516-c1afbf11d7f6","message":"Этот смс текст еще не прошёл модерацию. Сначала добавьте его через API - Шаблоны - Отправить шаблон или через кабинет my.eskiz.uz - СМС - Мои тексты.","status":"error"}
