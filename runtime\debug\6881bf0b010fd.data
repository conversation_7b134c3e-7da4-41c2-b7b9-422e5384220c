a:14:{s:6:"config";s:1743:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:1807:"a:1:{s:8:"messages";a:11:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753333514.97574;i:4;a:0:{}i:5;i:2610944;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753333514.979168;i:4;a:0:{}i:5;i:2789304;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753333514.979197;i:4;a:0:{}i:5;i:2790104;}i:3;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753333515.003313;i:4;a:0:{}i:5;i:4031656;}i:4;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753333515.011771;i:4;a:0:{}i:5;i:4537312;}i:5;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753333515.012673;i:4;a:0:{}i:5;i:4562144;}i:33;a:6:{i:0;s:41:"Route requested: 'employer/worker/search'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1753333515.018342;i:4;a:0:{}i:5;i:4875184;}i:34;a:6:{i:0;s:24:"Loading module: employer";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753333515.018363;i:4;a:0:{}i:5;i:4876824;}i:35;a:6:{i:0;s:36:"Route to run: employer/worker/search";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1753333515.031023;i:4;a:0:{}i:5;i:5226264;}i:36;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1753333515.03548;i:4;a:0:{}i:5;i:5375976;}i:37;a:6:{i:0;s:81:"Running action: app\modules\employer\controllers\WorkerController::actionSearch()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1753333515.03558;i:4;a:0:{}i:5;i:5442784;}}}";s:9:"profiling";s:83:"a:3:{s:6:"memory";i:5815008;s:4:"time";d:0.09176898002624512;s:8:"messages";a:0:{}}";s:2:"db";s:27:"a:1:{s:8:"messages";a:0:{}}";s:5:"event";s:2027:"a:11:{i:0;a:5:{s:4:"time";d:1753333515.017045;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1753333515.031354;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1753333515.03138;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:3;a:5:{s:4:"time";d:1753333515.035559;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\employer\controllers\WorkerController";}i:4;a:5:{s:4:"time";d:1753333515.037398;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\employer\controllers\WorkerController";}i:5;a:5:{s:4:"time";d:1753333515.038158;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:6;a:5:{s:4:"time";d:1753333515.038172;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1753333515.038185;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:8;a:5:{s:4:"time";d:1753333515.038195;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:9;a:5:{s:4:"time";d:1753333515.044159;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:10;a:5:{s:4:"time";d:1753333515.044235;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1753333514.955632;s:3:"end";d:1753333515.047473;s:6:"memory";i:5815008;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:5428:"a:3:{s:8:"messages";a:27:{i:6;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017776;i:4;a:0:{}i:5;i:4852592;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017801;i:4;a:0:{}i:5;i:4853344;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.01781;i:4;a:0:{}i:5;i:4854096;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017823;i:4;a:0:{}i:5;i:4855168;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017831;i:4;a:0:{}i:5;i:4855920;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017841;i:4;a:0:{}i:5;i:4856672;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017846;i:4;a:0:{}i:5;i:4857424;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017853;i:4;a:0:{}i:5;i:4858176;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017869;i:4;a:0:{}i:5;i:4858984;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:22:"POST worker/auth/login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.017984;i:4;a:0:{}i:5;i:4859784;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:23:"POST worker/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018025;i:4;a:0:{}i:5;i:4860584;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET worker/auth/verify";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018052;i:4;a:0:{}i:5;i:4862024;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:24:"POST worker/auth/refresh";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018067;i:4;a:0:{}i:5;i:4862832;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET worker/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018085;i:4;a:0:{}i:5;i:4863632;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET worker/vacancy/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018102;i:4;a:0:{}i:5;i:4864440;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET worker/vacancy/detail/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.01812;i:4;a:0:{}i:5;i:4865256;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET worker/profile/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018137;i:4;a:0:{}i:5;i:4866064;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT worker/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018151;i:4;a:0:{}i:5;i:4866872;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST worker/profile/upload-audio";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018164;i:4;a:0:{}i:5;i:4867688;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018178;i:4;a:0:{}i:5;i:4868496;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/auth/verify-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018191;i:4;a:0:{}i:5;i:4869304;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:40:"POST employer/auth/complete-registration";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018204;i:4;a:0:{}i:5;i:4870136;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/auth/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.01822;i:4;a:0:{}i:5;i:4870944;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:25:"POST employer/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018232;i:4;a:0:{}i:5;i:4871752;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/worker/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018284;i:4;a:0:{}i:5;i:4872560;}i:31;a:6:{i:0;s:52:"Request parsed with URL rule: employer/worker/search";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1753333515.018314;i:4;a:0:{}i:5;i:4873848;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/search";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753333515.018326;i:4;a:0:{}i:5;i:4874200;}}s:5:"route";s:22:"employer/worker/search";s:6:"action";s:65:"app\modules\employer\controllers\WorkerController::actionSearch()";}";s:7:"request";s:3071:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:422;s:14:"requestHeaders";a:7:{s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"2584b5f5-9840-43a8-bf92-454d53f172dd";s:4:"host";s:7:"vacanct";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:5:{s:32:"Access-Control-Allow-Credentials";s:5:"false";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6881bf0b010fd";s:16:"X-Debug-Duration";s:2:"89";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6881bf0b010fd";}s:5:"route";s:22:"employer/worker/search";s:6:"action";s:65:"app\modules\employer\controllers\WorkerController::actionSearch()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:37:{s:15:"REDIRECT_STATUS";s:3:"200";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"2584b5f5-9840-43a8-bf92-454d53f172dd";s:9:"HTTP_HOST";s:7:"vacanct";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:7:"vacanct";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:40:"D:/OSPanel/domains/ish_top/web/index.php";s:11:"REMOTE_PORT";s:5:"53120";s:12:"REDIRECT_URL";s:23:"/employer/worker/search";s:21:"REDIRECT_QUERY_STRING";s:28:"search=ererf&page=1&limit=10";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:28:"search=ererf&page=1&limit=10";s:11:"REQUEST_URI";s:52:"/employer/worker/search?search=ererf&page=1&limit=10";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1753333514.920118;s:12:"REQUEST_TIME";i:1753333514;}s:3:"GET";a:3:{s:6:"search";s:5:"ererf";s:4:"page";s:1:"1";s:5:"limit";s:2:"10";}s:4:"POST";a:0:{}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6881bf0b010fd";s:3:"url";s:66:"http://vacanct/employer/worker/search?search=ererf&page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753333514.920118;s:10:"statusCode";i:422;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:5815008;s:14:"processingTime";d:0.09176898002624512;}s:10:"exceptions";a:0:{}}