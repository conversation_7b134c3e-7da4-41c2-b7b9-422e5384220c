a:14:{s:6:"config";s:1743:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:14471:"a:1:{s:8:"messages";a:17:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753273161.047397;i:4;a:0:{}i:5;i:2611648;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753273161.051533;i:4;a:0:{}i:5;i:2790008;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753273161.051569;i:4;a:0:{}i:5;i:2790808;}i:3;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1753273161.078171;i:4;a:0:{}i:5;i:3733416;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753273161.091356;i:4;a:0:{}i:5;i:4181144;}i:5;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753273161.105135;i:4;a:0:{}i:5;i:4686720;}i:6;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753273161.106534;i:4;a:0:{}i:5;i:4711552;}i:90;a:6:{i:0;s:45:"Route requested: '/employer/auth/verify-code'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1753273161.112018;i:4;a:0:{}i:5;i:5014632;}i:91;a:6:{i:0;s:24:"Loading module: employer";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753273161.112031;i:4;a:0:{}i:5;i:5016280;}i:92;a:6:{i:0;s:39:"Route to run: employer/auth/verify-code";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1753273161.12158;i:4;a:0:{}i:5;i:5367112;}i:93;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1753273161.127989;i:4;a:0:{}i:5;i:5579768;}i:94;a:6:{i:0;s:83:"Running action: app\modules\employer\controllers\AuthController::actionVerifyCode()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1753273161.12815;i:4;a:0:{}i:5;i:5581040;}i:95;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1753273161.149345;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6330776;}i:98;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employer_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.216344;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6470320;}i:101;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employer_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.261842;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6583120;}i:104;a:6:{i:0;s:242:"INSERT INTO "employer_logs" ("action", "context", "employer_id", "device_info", "created_at") VALUES ('sms_verification_failed', '{"phone":"+998901234567","reason":"invalid_or_expired_code"}', NULL, NULL, '2025-07-23 15:19:21') RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.278005;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:109;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:7085640;}i:107;a:6:{i:0;s:5394:"PDOException: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "employer_id" of relation "employer_logs" violates not-null constraint
DETAIL:  Failing row contains (1, null, sms_verification_failed, {"phone":"+998901234567","reason":"invalid_or_expired_code"}, null, 2025-07-23 15:19:21, null). in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(431): yii\db\Command->queryInternal()
#3 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\pgsql\Schema.php(646): yii\db\Command->queryOne()
#4 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\ActiveRecord.php(604): yii\db\pgsql\Schema->insert()
#5 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\ActiveRecord.php(570): yii\db\ActiveRecord->insertInternal()
#6 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\BaseActiveRecord.php(688): yii\db\ActiveRecord->insert()
#7 D:\OSPanel\domains\ish_top\common\models\EmployerLog.php(109): yii\db\BaseActiveRecord->save()
#8 D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php(24): app\common\models\EmployerLog::logAction()
#9 D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php(268): app\modules\employer\services\LoggingService->logAction()
#10 D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php(186): app\modules\employer\services\LoggingService->logSmsVerificationFailed()
#11 [internal function]: app\modules\employer\controllers\AuthController->actionVerifyCode()
#12 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#13 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#14 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction()
#15 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction()
#16 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest()
#17 D:\OSPanel\domains\ish_top\web\index.php(12): yii\base\Application->run()
#18 {main}

Next yii\db\IntegrityException: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "employer_id" of relation "employer_logs" violates not-null constraint
DETAIL:  Failing row contains (1, null, sms_verification_failed, {"phone":"+998901234567","reason":"invalid_or_expired_code"}, null, 2025-07-23 15:19:21, null).
The SQL being executed was: INSERT INTO "employer_logs" ("action", "context", "employer_id", "device_info", "created_at") VALUES ('sms_verification_failed', '{"phone":"+998901234567","reason":"invalid_or_expired_code"}', NULL, NULL, '2025-07-23 15:19:21') RETURNING "id" in D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\Command.php(431): yii\db\Command->queryInternal()
#3 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\pgsql\Schema.php(646): yii\db\Command->queryOne()
#4 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\ActiveRecord.php(604): yii\db\pgsql\Schema->insert()
#5 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\ActiveRecord.php(570): yii\db\ActiveRecord->insertInternal()
#6 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\db\BaseActiveRecord.php(688): yii\db\ActiveRecord->insert()
#7 D:\OSPanel\domains\ish_top\common\models\EmployerLog.php(109): yii\db\BaseActiveRecord->save()
#8 D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php(24): app\common\models\EmployerLog::logAction()
#9 D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php(268): app\modules\employer\services\LoggingService->logAction()
#10 D:\OSPanel\domains\ish_top\modules\employer\controllers\AuthController.php(186): app\modules\employer\services\LoggingService->logSmsVerificationFailed()
#11 [internal function]: app\modules\employer\controllers\AuthController->actionVerifyCode()
#12 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#13 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#14 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction()
#15 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction()
#16 D:\OSPanel\domains\ish_top\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest()
#17 D:\OSPanel\domains\ish_top\web\index.php(12): yii\base\Application->run()
#18 {main}
Additional Information:
Array
(
    [0] => 23502
    [1] => 7
    [2] => ERROR:  null value in column "employer_id" of relation "employer_logs" violates not-null constraint
DETAIL:  Failing row contains (1, null, sms_verification_failed, {"phone":"+998901234567","reason":"invalid_or_expired_code"}, null, 2025-07-23 15:19:21, null).
)
";i:1;i:1;i:2;s:25:"yii\db\IntegrityException";i:3;d:1753273161.372257;i:4;a:0:{}i:5;i:6904328;}}}";s:9:"profiling";s:14120:"a:3:{s:6:"memory";i:7531152;s:4:"time";d:0.38320302963256836;s:8:"messages";a:8:{i:96;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1753273161.149393;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6332656;}i:97;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1753273161.214272;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6335328;}i:99;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employer_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.21641;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6538184;}i:100;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employer_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.25902;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6552728;}i:102;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employer_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.261884;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6585360;}i:103;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employer_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.267465;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6588568;}i:105;a:6:{i:0;s:242:"INSERT INTO "employer_logs" ("action", "context", "employer_id", "device_info", "created_at") VALUES ('sms_verification_failed', '{"phone":"+998901234567","reason":"invalid_or_expired_code"}', NULL, NULL, '2025-07-23 15:19:21') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.278112;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:109;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:7087144;}i:106;a:6:{i:0;s:242:"INSERT INTO "employer_logs" ("action", "context", "employer_id", "device_info", "created_at") VALUES ('sms_verification_failed', '{"phone":"+998901234567","reason":"invalid_or_expired_code"}', NULL, NULL, '2025-07-23 15:19:21') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.371643;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:109;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:7185784;}}}";s:2:"db";s:12438:"a:1:{s:8:"messages";a:6:{i:99;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employer_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.21641;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6538184;}i:100;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'employer_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.25902;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6552728;}i:102;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employer_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.261884;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6585360;}i:103;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='employer_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.267465;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:104;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:6588568;}i:105;a:6:{i:0;s:242:"INSERT INTO "employer_logs" ("action", "context", "employer_id", "device_info", "created_at") VALUES ('sms_verification_failed', '{"phone":"+998901234567","reason":"invalid_or_expired_code"}', NULL, NULL, '2025-07-23 15:19:21') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.278112;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:109;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:7087144;}i:106;a:6:{i:0;s:242:"INSERT INTO "employer_logs" ("action", "context", "employer_id", "device_info", "created_at") VALUES ('sms_verification_failed', '{"phone":"+998901234567","reason":"invalid_or_expired_code"}', NULL, NULL, '2025-07-23 15:19:21') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753273161.371643;i:4;a:3:{i:0;a:5:{s:4:"file";s:56:"D:\OSPanel\domains\ish_top\common\models\EmployerLog.php";s:4:"line";i:109;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:24;s:8:"function";s:9:"logAction";s:5:"class";s:29:"app\common\models\EmployerLog";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\ish_top\modules\employer\services\LoggingService.php";s:4:"line";i:268;s:8:"function";s:9:"logAction";s:5:"class";s:44:"app\modules\employer\services\LoggingService";s:4:"type";s:2:"->";}}i:5;i:7185784;}}}";s:5:"event";s:2187:"a:12:{i:0;a:5:{s:4:"time";d:1753273161.110703;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1753273161.121906;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1753273161.121928;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:3;a:5:{s:4:"time";d:1753273161.128121;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\employer\controllers\AuthController";}i:4;a:5:{s:4:"time";d:1753273161.142331;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:29:"app\common\models\EmployerLog";}i:5;a:5:{s:4:"time";d:1753273161.214252;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:1753273161.269045;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:29:"app\common\models\EmployerLog";}i:7;a:5:{s:4:"time";d:1753273161.273055;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:29:"app\common\models\EmployerLog";}i:8;a:5:{s:4:"time";d:1753273161.273132;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:29:"app\common\models\EmployerLog";}i:9;a:5:{s:4:"time";d:1753273161.372426;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:10;a:5:{s:4:"time";d:1753273161.380954;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:11;a:5:{s:4:"time";d:1753273161.381088;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1753273161.019922;s:3:"end";d:1753273161.403371;s:6:"memory";i:7531152;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:16625:"a:3:{s:8:"messages";a:83:{i:7;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111456;i:4;a:0:{}i:5;i:4941672;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.11148;i:4;a:0:{}i:5;i:4942424;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111491;i:4;a:0:{}i:5;i:4943496;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111497;i:4;a:0:{}i:5;i:4944248;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111505;i:4;a:0:{}i:5;i:4945000;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111514;i:4;a:0:{}i:5;i:4945752;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.11152;i:4;a:0:{}i:5;i:4946504;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111526;i:4;a:0:{}i:5;i:4947256;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111546;i:4;a:0:{}i:5;i:4948064;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:22:"POST worker/auth/login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111554;i:4;a:0:{}i:5;i:4948864;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:23:"POST worker/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111562;i:4;a:0:{}i:5;i:4950304;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET worker/auth/verify";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111568;i:4;a:0:{}i:5;i:4951104;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:24:"POST worker/auth/refresh";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111575;i:4;a:0:{}i:5;i:4951912;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET worker/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111581;i:4;a:0:{}i:5;i:4952712;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET worker/vacancy/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111587;i:4;a:0:{}i:5;i:4953520;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET worker/vacancy/detail/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111593;i:4;a:0:{}i:5;i:4954336;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET worker/profile/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111598;i:4;a:0:{}i:5;i:4955144;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT worker/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111603;i:4;a:0:{}i:5;i:4955952;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST worker/profile/upload-audio";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111611;i:4;a:0:{}i:5;i:4956768;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111619;i:4;a:0:{}i:5;i:4957576;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/auth/verify-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111628;i:4;a:0:{}i:5;i:4958384;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:40:"POST employer/auth/complete-registration";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111636;i:4;a:0:{}i:5;i:4959216;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/auth/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111642;i:4;a:0:{}i:5;i:4960024;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:25:"POST employer/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.11165;i:4;a:0:{}i:5;i:4960832;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/worker/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111655;i:4;a:0:{}i:5;i:4961640;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111661;i:4;a:0:{}i:5;i:4962448;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111667;i:4;a:0:{}i:5;i:4964536;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/worker/professions";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111672;i:4;a:0:{}i:5;i:4965344;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:33:"GET employer/worker/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111677;i:4;a:0:{}i:5;i:4966160;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/worker/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111683;i:4;a:0:{}i:5;i:4966968;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/worker/check-access";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111688;i:4;a:0:{}i:5;i:4967784;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/worker/unlock-contact";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111693;i:4;a:0:{}i:5;i:4968600;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST employer/favorite/add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111701;i:4;a:0:{}i:5;i:4969408;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111708;i:4;a:0:{}i:5;i:4970216;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/favorite/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111714;i:4;a:0:{}i:5;i:4971024;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/favorite/toggle";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111721;i:4;a:0:{}i:5;i:4971832;}i:43;a:6:{i:0;a:3:{s:4:"rule";s:31:"POST employer/favorite/bulk-add";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111728;i:4;a:0:{}i:5;i:4972640;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:34:"POST employer/favorite/bulk-remove";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111735;i:4;a:0:{}i:5;i:4973456;}i:45;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/favorite/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.11174;i:4;a:0:{}i:5;i:4974272;}i:46;a:6:{i:0;a:3:{s:4:"rule";s:35:"GET employer/favorite/by-profession";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111746;i:4;a:0:{}i:5;i:4975088;}i:47;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/profile/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111751;i:4;a:0:{}i:5;i:4975896;}i:48;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111756;i:4;a:0:{}i:5;i:4976704;}i:49;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111763;i:4;a:0:{}i:5;i:4977512;}i:50;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/profile/change-language";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111769;i:4;a:0:{}i:5;i:4978328;}i:51;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/profile/languages";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111775;i:4;a:0:{}i:5;i:4979136;}i:52;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/profile/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111782;i:4;a:0:{}i:5;i:4979944;}i:53;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111789;i:4;a:0:{}i:5;i:4980752;}i:54;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/vacancy/view";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111794;i:4;a:0:{}i:5;i:4981560;}i:55;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/create";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111802;i:4;a:0:{}i:5;i:4982368;}i:56;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT employer/vacancy/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111808;i:4;a:0:{}i:5;i:4983176;}i:57;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111815;i:4;a:0:{}i:5;i:4983984;}i:58;a:6:{i:0;a:3:{s:4:"rule";s:30:"DELETE employer/vacancy/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.11182;i:4;a:0:{}i:5;i:4984792;}i:59;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/vacancy/delete";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111827;i:4;a:0:{}i:5;i:4985600;}i:60;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/vacancy/statuses";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111833;i:4;a:0:{}i:5;i:4986408;}i:61;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/access/filter-page";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111838;i:4;a:0:{}i:5;i:4987216;}i:62;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/access/calculate-price";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111845;i:4;a:0:{}i:5;i:4988032;}i:63;a:6:{i:0;a:3:{s:4:"rule";s:29:"POST employer/access/purchase";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111852;i:4;a:0:{}i:5;i:4988840;}i:64;a:6:{i:0;a:3:{s:4:"rule";s:32:"GET employer/access/my-purchases";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111857;i:4;a:0:{}i:5;i:4989656;}i:65;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/access/available";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111863;i:4;a:0:{}i:5;i:4996096;}i:66;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/access/unlock-contact";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111869;i:4;a:0:{}i:5;i:4996912;}i:67;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET employer/access/contact-status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111874;i:4;a:0:{}i:5;i:4997728;}i:68;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/access/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111879;i:4;a:0:{}i:5;i:4998536;}i:69;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET employer/tariff/plans";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111884;i:4;a:0:{}i:5;i:4999344;}i:70;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/tariff/detail";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.11189;i:4;a:0:{}i:5;i:5000152;}i:71;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET employer/tariff/compare";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111895;i:4;a:0:{}i:5;i:5000960;}i:72;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/tariff/filtered";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.1119;i:4;a:0:{}i:5;i:5001768;}i:73;a:6:{i:0;a:3:{s:4:"rule";s:30:"GET employer/tariff/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111905;i:4;a:0:{}i:5;i:5002576;}i:74;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/tariff/recommended";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111911;i:4;a:0:{}i:5;i:5003384;}i:75;a:6:{i:0;a:3:{s:4:"rule";s:29:"GET employer/tariff/discounts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111916;i:4;a:0:{}i:5;i:5004192;}i:76;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/payment/methods";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111922;i:4;a:0:{}i:5;i:5005000;}i:77;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET employer/payment/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111928;i:4;a:0:{}i:5;i:5005808;}i:78;a:6:{i:0;a:3:{s:4:"rule";s:28:"GET employer/payment/history";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111933;i:4;a:0:{}i:5;i:5006616;}i:79;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/payment/callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.11194;i:4;a:0:{}i:5;i:5007424;}i:80;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/payment/click-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111947;i:4;a:0:{}i:5;i:5008240;}i:81;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST employer/payment/payme-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111954;i:4;a:0:{}i:5;i:5009056;}i:82;a:6:{i:0;a:3:{s:4:"rule";s:37:"POST employer/payment/uzcard-callback";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.11196;i:4;a:0:{}i:5;i:5009872;}i:83;a:6:{i:0;a:3:{s:4:"rule";s:31:"GET employer/payment/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111966;i:4;a:0:{}i:5;i:5010680;}i:84;a:6:{i:0;a:3:{s:4:"rule";s:27:"POST employer/payment/retry";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111973;i:4;a:0:{}i:5;i:5011488;}i:85;a:6:{i:0;a:3:{s:4:"rule";s:21:"employer/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111979;i:4;a:0:{}i:5;i:5012240;}i:86;a:6:{i:0;a:3:{s:4:"rule";s:29:"employer/vacancy/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111985;i:4;a:0:{}i:5;i:5012992;}i:87;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.111994;i:4;a:0:{}i:5;i:5013744;}i:88;a:6:{i:0;a:3:{s:4:"rule";s:12:"<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.112001;i:4;a:0:{}i:5;i:5014496;}i:89;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753273161.112006;i:4;a:0:{}i:5;i:5014872;}}s:5:"route";s:25:"employer/auth/verify-code";s:6:"action";s:67:"app\modules\employer\controllers\AuthController::actionVerifyCode()";}";s:7:"request";s:3534:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:500;s:14:"requestHeaders";a:10:{s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"e71f4abb-7613-49b7-bb4b-0945b1ea795a";s:4:"host";s:7:"vacanct";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:2:"52";s:6:"cookie";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:32:"Access-Control-Allow-Credentials";s:5:"false";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6880d34916a9c";s:16:"X-Debug-Duration";s:3:"362";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6880d34916a9c";}s:5:"route";s:25:"employer/auth/verify-code";s:6:"action";s:67:"app\modules\employer\controllers\AuthController::actionVerifyCode()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:52:"{
    "phone": "+998901234567",
    "code": "1234"
}";s:7:"Decoded";a:0:{}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"e71f4abb-7613-49b7-bb4b-0945b1ea795a";s:9:"HTTP_HOST";s:7:"vacanct";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:2:"52";s:11:"HTTP_COOKIE";s:42:"PHPSESSID=gnoa088o0hon1vgqigs0tb9cu9vd163k";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:7:"vacanct";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:40:"D:/OSPanel/domains/ish_top/web/index.php";s:11:"REMOTE_PORT";s:5:"62204";s:12:"REDIRECT_URL";s:26:"/employer/auth/verify-code";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:27:"//employer/auth/verify-code";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1753273160.975351;s:12:"REQUEST_TIME";i:1753273160;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:1:{s:9:"PHPSESSID";s:32:"gnoa088o0hon1vgqigs0tb9cu9vd163k";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6880d34916a9c";s:3:"url";s:41:"http://vacanct//employer/auth/verify-code";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753273160.975351;s:10:"statusCode";i:500;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7531152;s:14:"processingTime";d:0.38320302963256836;}s:10:"exceptions";a:0:{}}