a:14:{s:6:"config";s:1743:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:6:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:56:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:54:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:64:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:61:"D:\OSPanel\domains\ish_top\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:7273:"a:1:{s:8:"messages";a:16:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753335879.45371;i:4;a:0:{}i:5;i:2611024;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753335879.458044;i:4;a:0:{}i:5;i:2789384;}i:2;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753335879.458111;i:4;a:0:{}i:5;i:2790184;}i:3;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753335879.486655;i:4;a:0:{}i:5;i:4031736;}i:4;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753335879.499622;i:4;a:0:{}i:5;i:4537392;}i:5;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753335879.501042;i:4;a:0:{}i:5;i:4562224;}i:33;a:6:{i:0;s:41:"Route requested: 'employer/worker/search'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1753335879.51276;i:4;a:0:{}i:5;i:4875264;}i:34;a:6:{i:0;s:24:"Loading module: employer";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753335879.512785;i:4;a:0:{}i:5;i:4876904;}i:35;a:6:{i:0;s:36:"Route to run: employer/worker/search";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1753335879.534776;i:4;a:0:{}i:5;i:5230552;}i:36;a:6:{i:0;s:39:"Rate limit skipped: user not logged in.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1753335879.544801;i:4;a:0:{}i:5;i:5380264;}i:37;a:6:{i:0;s:81:"Running action: app\modules\employer\controllers\WorkerController::actionSearch()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1753335879.54503;i:4;a:0:{}i:5;i:5447072;}i:38;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1753335879.582457;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:100;s:8:"function";s:9:"getRawSql";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:6995064;}i:41;a:6:{i:0;s:1073:"Search Query SQL: SELECT "w".*, 
                    CASE 
                        WHEN LOWER(p.name_uz) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_ru) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_en) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_uz) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_ru) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_en) ILIKE '%dasturchi%' THEN 2
                        ELSE 1
                    END AS relevance_score
                 FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0) ORDER BY "relevance_score" DESC, "w"."created_at" DESC";i:1;i:4;i:2;s:12:"search-debug";i:3;d:1753335879.659735;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:100;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:6992520;}i:42;a:6:{i:0;s:454:"SELECT COUNT(*) FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.660269;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:103;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7001424;}i:45;a:6:{i:0;s:1064:"SELECT "w".*, 
                    CASE 
                        WHEN LOWER(p.name_uz) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_ru) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_en) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_uz) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_ru) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_en) ILIKE '%dasturchi%' THEN 2
                        ELSE 1
                    END AS relevance_score
                 FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0) ORDER BY "relevance_score" DESC, "w"."created_at" DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.686332;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7074584;}i:48;a:6:{i:0;s:33:"SELECT * FROM "workers" WHERE 0=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.697482;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7285080;}}}";s:9:"profiling";s:7782:"a:3:{s:6:"memory";i:7703904;s:4:"time";d:0.2777688503265381;s:8:"messages";a:8:{i:39;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1753335879.582485;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:100;s:8:"function";s:9:"getRawSql";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:6996568;}i:40;a:6:{i:0;s:58:"Opening DB connection: pgsql:host=localhost;dbname=ish_top";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1753335879.659576;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:100;s:8:"function";s:9:"getRawSql";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:6998864;}i:43;a:6:{i:0;s:454:"SELECT COUNT(*) FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.660365;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:103;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7005096;}i:44;a:6:{i:0;s:454:"SELECT COUNT(*) FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.685582;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:103;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7072520;}i:46;a:6:{i:0;s:1064:"SELECT "w".*, 
                    CASE 
                        WHEN LOWER(p.name_uz) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_ru) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_en) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_uz) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_ru) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_en) ILIKE '%dasturchi%' THEN 2
                        ELSE 1
                    END AS relevance_score
                 FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0) ORDER BY "relevance_score" DESC, "w"."created_at" DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.686381;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7079304;}i:47;a:6:{i:0;s:1064:"SELECT "w".*, 
                    CASE 
                        WHEN LOWER(p.name_uz) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_ru) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_en) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_uz) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_ru) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_en) ILIKE '%dasturchi%' THEN 2
                        ELSE 1
                    END AS relevance_score
                 FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0) ORDER BY "relevance_score" DESC, "w"."created_at" DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.692124;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7082536;}i:49;a:6:{i:0;s:33:"SELECT * FROM "workers" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.697521;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7286944;}i:50;a:6:{i:0;s:33:"SELECT * FROM "workers" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.698387;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7289656;}}}";s:2:"db";s:6481:"a:1:{s:8:"messages";a:6:{i:43;a:6:{i:0;s:454:"SELECT COUNT(*) FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.660365;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:103;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7005096;}i:44;a:6:{i:0;s:454:"SELECT COUNT(*) FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.685582;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:103;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7072520;}i:46;a:6:{i:0;s:1064:"SELECT "w".*, 
                    CASE 
                        WHEN LOWER(p.name_uz) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_ru) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_en) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_uz) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_ru) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_en) ILIKE '%dasturchi%' THEN 2
                        ELSE 1
                    END AS relevance_score
                 FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0) ORDER BY "relevance_score" DESC, "w"."created_at" DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.686381;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7079304;}i:47;a:6:{i:0;s:1064:"SELECT "w".*, 
                    CASE 
                        WHEN LOWER(p.name_uz) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_ru) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_en) = 'dasturchi' THEN 3
                        WHEN LOWER(p.name_uz) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_ru) ILIKE '%dasturchi%' THEN 2
                        WHEN LOWER(p.name_en) ILIKE '%dasturchi%' THEN 2
                        ELSE 1
                    END AS relevance_score
                 FROM "workers" "w" INNER JOIN "worker_professions" "wp" ON w.id = wp.worker_id INNER JOIN "professions" "p" ON wp.profession_id = p.id WHERE ("w"."deleted_at" IS NULL) AND ("w"."profile_status"=1) AND (("p"."name_uz" ILIKE '%\%dasturchi\%%') OR ("p"."name_ru" ILIKE '%\%dasturchi\%%') OR ("p"."name_en" ILIKE '%\%dasturchi\%%')) AND ("w"."age" >= 0) AND ("w"."age" <= 0) AND ("w"."experience_years" >= 0) AND ("w"."experience_years" <= 0) ORDER BY "relevance_score" DESC, "w"."created_at" DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.692124;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:106;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7082536;}i:49;a:6:{i:0;s:33:"SELECT * FROM "workers" WHERE 0=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.697521;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7286944;}i:50;a:6:{i:0;s:33:"SELECT * FROM "workers" WHERE 0=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753335879.698387;i:4;a:2:{i:0;a:5:{s:4:"file";s:74:"D:\OSPanel\domains\ish_top\modules\employer\services\WorkerViewService.php";s:4:"line";i:114;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:76:"D:\OSPanel\domains\ish_top\modules\employer\controllers\WorkerController.php";s:4:"line";i:115;s:8:"function";s:13:"searchWorkers";s:5:"class";s:47:"app\modules\employer\services\WorkerViewService";s:4:"type";s:2:"->";}}i:5;i:7289656;}}}";s:5:"event";s:2363:"a:13:{i:0;a:5:{s:4:"time";d:1753335879.510627;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1753335879.536169;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1753335879.536206;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:3;a:5:{s:4:"time";d:1753335879.544986;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\employer\controllers\WorkerController";}i:4;a:5:{s:4:"time";d:1753335879.659548;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:1753335879.695963;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:6;a:5:{s:4:"time";d:1753335879.699993;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\employer\controllers\WorkerController";}i:7;a:5:{s:4:"time";d:1753335879.700577;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"app\modules\employer\Module";}i:8;a:5:{s:4:"time";d:1753335879.70059;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:9;a:5:{s:4:"time";d:1753335879.700604;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:10;a:5:{s:4:"time";d:1753335879.700614;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:11;a:5:{s:4:"time";d:1753335879.706799;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:12;a:5:{s:4:"time";d:1753335879.706905;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:90:"a:3:{s:5:"start";d:1753335879.43191;s:3:"end";d:1753335879.709795;s:6:"memory";i:7703904;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:5430:"a:3:{s:8:"messages";a:27:{i:6;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512056;i:4;a:0:{}i:5;i:4852672;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512139;i:4;a:0:{}i:5;i:4853424;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512155;i:4;a:0:{}i:5;i:4854176;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512167;i:4;a:0:{}i:5;i:4855248;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512178;i:4;a:0:{}i:5;i:4856000;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:16:"telegram/webhook";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512193;i:4;a:0:{}i:5;i:4856752;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:34:"telegram/registration/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512203;i:4;a:0:{}i:5;i:4857504;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:32:"telegram/profession/<action:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512213;i:4;a:0:{}i:5;i:4858256;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST worker/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.51224;i:4;a:0:{}i:5;i:4859064;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:22:"POST worker/auth/login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512252;i:4;a:0:{}i:5;i:4859864;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:23:"POST worker/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512261;i:4;a:0:{}i:5;i:4860664;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET worker/auth/verify";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512273;i:4;a:0:{}i:5;i:4862104;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:24:"POST worker/auth/refresh";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512281;i:4;a:0:{}i:5;i:4862912;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET worker/vacancy/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512292;i:4;a:0:{}i:5;i:4863712;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET worker/vacancy/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512393;i:4;a:0:{}i:5;i:4864520;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:34:"GET worker/vacancy/detail/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512422;i:4;a:0:{}i:5;i:4865336;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET worker/profile/index";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512618;i:4;a:0:{}i:5;i:4866144;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT worker/profile/update";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512637;i:4;a:0:{}i:5;i:4866952;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST worker/profile/upload-audio";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512645;i:4;a:0:{}i:5;i:4867768;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST employer/auth/send-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512657;i:4;a:0:{}i:5;i:4868576;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:30:"POST employer/auth/verify-code";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512665;i:4;a:0:{}i:5;i:4869384;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:40:"POST employer/auth/complete-registration";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512673;i:4;a:0:{}i:5;i:4870216;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/auth/status";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512687;i:4;a:0:{}i:5;i:4871024;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:25:"POST employer/auth/logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512695;i:4;a:0:{}i:5;i:4871832;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET employer/worker/list";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512705;i:4;a:0:{}i:5;i:4872640;}i:31;a:6:{i:0;s:52:"Request parsed with URL rule: employer/worker/search";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1753335879.512729;i:4;a:0:{}i:5;i:4873928;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET employer/worker/search";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753335879.512739;i:4;a:0:{}i:5;i:4874280;}}s:5:"route";s:22:"employer/worker/search";s:6:"action";s:65:"app\modules\employer\controllers\WorkerController::actionSearch()";}";s:7:"request";s:3088:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:7:{s:10:"user-agent";s:21:"PostmanRuntime/7.44.1";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"72d44501-ec95-43e9-9a0b-6b31493f8fce";s:4:"host";s:7:"vacanct";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:5:{s:32:"Access-Control-Allow-Credentials";s:5:"false";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6881c8477712d";s:16:"X-Debug-Duration";s:3:"276";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6881c8477712d";}s:5:"route";s:22:"employer/worker/search";s:6:"action";s:65:"app\modules\employer\controllers\WorkerController::actionSearch()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:37:{s:15:"REDIRECT_STATUS";s:3:"200";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.1";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"72d44501-ec95-43e9-9a0b-6b31493f8fce";s:9:"HTTP_HOST";s:7:"vacanct";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:7:"vacanct";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:30:"D:/OSPanel/domains/ish_top/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:40:"D:/OSPanel/domains/ish_top/web/index.php";s:11:"REMOTE_PORT";s:5:"59328";s:12:"REDIRECT_URL";s:23:"/employer/worker/search";s:21:"REDIRECT_QUERY_STRING";s:32:"search=dasturchi&page=1&limit=10";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:32:"search=dasturchi&page=1&limit=10";s:11:"REQUEST_URI";s:56:"/employer/worker/search?search=dasturchi&page=1&limit=10";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1753335879.402028;s:12:"REQUEST_TIME";i:1753335879;}s:3:"GET";a:3:{s:6:"search";s:9:"dasturchi";s:4:"page";s:1:"1";s:5:"limit";s:2:"10";}s:4:"POST";a:0:{}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6881c8477712d";s:3:"url";s:70:"http://vacanct/employer/worker/search?search=dasturchi&page=1&limit=10";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1753335879.402028;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7703904;s:14:"processingTime";d:0.2777688503265381;}s:10:"exceptions";a:0:{}}