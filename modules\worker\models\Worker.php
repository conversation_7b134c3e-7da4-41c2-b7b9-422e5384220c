<?php

namespace app\modules\worker\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель работника
 * 
 * @property int $id
 * @property int $chat_id
 * @property string $name
 * @property string $phone
 * @property int $age
 * @property float $lat
 * @property float $long
 * @property int $experience_years
 * @property string $audio_file_url
 * @property string $about
 * @property string $language
 * @property int $status
 * @property string $profile_status
 * @property string $auth_token
 * @property string $token_expires_at
 * @property string $created_at
 * @property string $deleted_at
 */
class Worker extends ActiveRecord
{
    // Константы для статуса профиля
    public const PROFILE_STATUS_INCOMPLETE = 0;
    public const PROFILE_STATUS_COMPLETE = 1;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%workers}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['chat_id', 'age', 'experience_years', 'status'], 'integer'],
            [['lat', 'long'], 'number'],
            [['about'], 'string'],
            [['created_at', 'deleted_at', 'token_expires_at'], 'safe'],
            [['auth_token'], 'string', 'max' => 255],
            [['chat_id'], 'string', 'max' => 50],
            [['name'], 'string', 'max' => 100],
            [['phone'], 'string', 'max' => 20],
            [['audio_file_url'], 'string', 'max' => 255],
            [['language'], 'string', 'max' => 10],
            [['profile_status'], 'integer'],
            [['profile_status'], 'in', 'range' => [self::PROFILE_STATUS_INCOMPLETE, self::PROFILE_STATUS_COMPLETE]],
            [['profile_status'], 'default', 'value' => self::PROFILE_STATUS_INCOMPLETE],
            [['chat_id'], 'unique'],
            [['phone'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => \Yii::t('app', 'id'),
            'chat_id' => \Yii::t('app', 'telegram chat id'),
            'name' => \Yii::t('app', 'name'),
            'phone' => \Yii::t('app', 'phone'),
            'age' => \Yii::t('app', 'age'),
            'lat' => \Yii::t('app', 'latitude'),
            'long' => \Yii::t('app', 'longitude'),
            'experience_years' => \Yii::t('app', 'years of experience'),
            'audio_file_url' => \Yii::t('app', 'audio file'),
            'about' => \Yii::t('app', 'about'),
            'language' => \Yii::t('app', 'language'),
            'status' => \Yii::t('app', 'status'),
            'profile_status' => \Yii::t('app', 'profile status'),
            'auth_token' => \Yii::t('app', 'auth token'),
            'token_expires_at' => \Yii::t('app', 'token expiration time'),
            'created_at' => \Yii::t('app', 'created at'),
            'deleted_at' => \Yii::t('app', 'deleted at'),
        ];

    }

    /**
     * Получение профессий работника
     */
    public function getProfessions()
    {
        return $this->hasMany(\app\modules\worker\models\Profession::class, ['id' => 'profession_id'])
            ->viaTable('{{%worker_professions}}', ['worker_id' => 'id']);
    }

    /**
     * Получение избранных вакансий
     */
    public function getFavoriteVacancies()
    {
        return $this->hasMany(\app\modules\employer\models\Vacancy::class, ['id' => 'vacancy_id'])
            ->viaTable('{{%worker_favorites}}', ['worker_id' => 'id'])
            ->where(['{{%worker_favorites}}.deleted_at' => null]);
    }

    /**
     * Генерация нового токена авторизации
     *
     * @param int $expiresInHours Время жизни токена в часах (по умолчанию 24 часа)
     * @return string
     */
    public function generateAuthToken($expiresInHours = 24)
    {
        $this->auth_token = \Yii::$app->security->generateRandomString(64);
        $this->token_expires_at = date('Y-m-d H:i:s', time() + ($expiresInHours * 3600));
        $this->save(false);

        return $this->auth_token;
    }

    /**
     * Проверка валидности токена
     *
     * @return bool
     */
    public function isTokenValid()
    {
        if (empty($this->auth_token) || empty($this->token_expires_at)) {
            return false;
        }

        return strtotime($this->token_expires_at) > time();
    }

    /**
     * Очистка токена
     */
    public function clearAuthToken()
    {
        $this->auth_token = null;
        $this->token_expires_at = null;
        $this->save(false);
    }

    /**
     * Поиск работника по токену
     *
     * @param string $token
     * @return static|null
     */
    public static function findByAuthToken($token)
    {
        if (empty($token)) {
            return null;
        }

        $worker = static::findOne([
            'auth_token' => $token,
            'deleted_at' => null
        ]);

        if ($worker && $worker->isTokenValid()) {
            return $worker;
        }

        return null;
    }

    /**
     * Проверить, завершен ли профиль
     *
     * @return bool
     */
    public function isProfileComplete()
    {
        return $this->profile_status === self::PROFILE_STATUS_COMPLETE;
    }

    /**
     * Пометить профиль как завершенный
     */
    public function markProfileComplete()
    {
        $this->profile_status = self::PROFILE_STATUS_COMPLETE;
        $this->save(false);
    }

    /**
     * Проверить, является ли профиль минимальным (только телефон)
     *
     * @return bool
     */
    public function isMinimalProfile()
    {
        return empty($this->name) &&
               empty($this->age) &&
               empty($this->about) &&
               $this->profile_status === self::PROFILE_STATUS_INCOMPLETE;
    }
}