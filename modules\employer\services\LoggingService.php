<?php

namespace app\modules\employer\services;

use app\modules\employer\models\Employer;
use app\common\models\EmployerLog;

/**
 * Сервис логирования действий работодателей
 */
class LoggingService
{
    /**
     * Логировать действие работодателя
     * 
     * @param int|null $employerId
     * @param string $action
     * @param array $context
     * @param string|null $deviceInfo
     */
    public function logAction($employerId, string $action, array $context = [], string $deviceInfo = null)
    {
        EmployerLog::logAction(
            $action,
            $context,
            $employerId,
            $deviceInfo
        );
    }

    /**
     * Логировать авторизацию работодателя
     * 
     * @param Employer $employer
     * @param string $phone
     * @param string $tokenExpiresAt
     */
    public function logLogin(Employer $employer, string $phone, string $tokenExpiresAt)
    {
        $this->logAction(
            $employer->id,
            'api_login',
            [
                'phone' => $phone,
                'token_expires_at' => $tokenExpiresAt
            ]
        );
    }

    /**
     * Логировать выход работодателя из системы
     * 
     * @param Employer $employer
     */
    public function logLogout(Employer $employer)
    {
        $this->logAction(
            $employer->id,
            'api_logout',
            ['token_cleared' => true]
        );
    }

    /**
     * Логировать обновление токена
     * 
     * @param Employer $employer
     * @param string $oldToken
     * @param string $newExpiresAt
     */
    public function logTokenRefresh(Employer $employer, string $oldToken, string $newExpiresAt)
    {
        $this->logAction(
            $employer->id,
            'api_token_refresh',
            [
                'old_token' => substr($oldToken, 0, 10) . '...',
                'new_expires_at' => $newExpiresAt
            ]
        );
    }

    /**
     * Логировать просмотр профиля
     * 
     * @param Employer $employer
     */
    public function logProfileView(Employer $employer)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_PROFILE_VIEWED,
            ['employer_id' => $employer->id]
        );
    }

    /**
     * Логировать обновление профиля
     * 
     * @param Employer $employer
     * @param array $updatedFields
     */
    public function logProfileUpdate(Employer $employer, array $updatedFields)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_PROFILE_UPDATED,
            [
                'updated_fields' => $updatedFields,
                'employer_id' => $employer->id
            ]
        );
    }

    /**
     * Логировать поиск работников
     * 
     * @param Employer $employer
     * @param array $filters
     * @param int $resultsCount
     */
    public function logWorkerSearch(Employer $employer, array $filters, int $resultsCount)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_WORKER_SEARCHED,
            [
                'filters' => $filters,
                'results_count' => $resultsCount
            ]
        );
    }

    /**
     * Логировать просмотр работника
     * 
     * @param Employer $employer
     * @param int $workerId
     */
    public function logWorkerView(Employer $employer, int $workerId)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_WORKER_VIEWED,
            ['worker_id' => $workerId]
        );
    }

    /**
     * Логировать добавление работника в избранные
     * 
     * @param Employer $employer
     * @param int $workerId
     */
    public function logWorkerFavorited(Employer $employer, int $workerId)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_WORKER_FAVORITED,
            ['worker_id' => $workerId]
        );
    }

    /**
     * Логировать создание вакансии
     * 
     * @param Employer $employer
     * @param int $vacancyId
     * @param array $vacancyData
     */
    public function logVacancyCreated(Employer $employer, int $vacancyId, array $vacancyData)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_VACANCY_CREATED,
            [
                'vacancy_id' => $vacancyId,
                'vacancy_data' => $vacancyData
            ]
        );
    }

    /**
     * Логировать обновление вакансии
     * 
     * @param Employer $employer
     * @param int $vacancyId
     * @param array $updatedFields
     */
    public function logVacancyUpdated(Employer $employer, int $vacancyId, array $updatedFields)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_VACANCY_UPDATED,
            [
                'vacancy_id' => $vacancyId,
                'updated_fields' => $updatedFields
            ]
        );
    }

    /**
     * Логировать удаление вакансии
     * 
     * @param Employer $employer
     * @param int $vacancyId
     */
    public function logVacancyDeleted(Employer $employer, int $vacancyId)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_VACANCY_DELETED,
            ['vacancy_id' => $vacancyId]
        );
    }

    /**
     * Логировать отправку SMS кода
     * 
     * @param int|null $employerId
     * @param string $phone
     * @param bool $isNewRegistration
     */
    public function logSmsCodeSent($employerId, string $phone, bool $isNewRegistration)
    {
        $this->logAction(
            $employerId,
            'sms_code_sent',
            [
                'phone' => $phone,
                'is_new_registration' => $isNewRegistration
            ]
        );
    }

    /**
     * Логировать успешную верификацию SMS
     * 
     * @param Employer $employer
     * @param string $phone
     * @param bool $isNewRegistration
     */
    public function logSmsVerificationSuccess(Employer $employer, string $phone, bool $isNewRegistration)
    {
        $this->logAction(
            $employer->id,
            'sms_verification_success',
            [
                'phone' => $phone,
                'is_new_registration' => $isNewRegistration
            ]
        );
    }

    /**
     * Логировать неудачную верификацию SMS
     * 
     * @param string $phone
     * @param string $reason
     * @param int|null $employerId Опциональный ID работодателя
     */
    public function logSmsVerificationFailed(string $phone, string $reason, $employerId = null)
    {
        // Если есть employer_id, логируем в employer_logs, иначе пропускаем
        if ($employerId !== null) {
            $this->logAction(
                $employerId,
                'sms_verification_failed',
                [
                    'phone' => $phone,
                    'reason' => $reason
                ]
            );
        } else {
            // Логируем в обычный лог файл, если нет employer_id
            \Yii::warning("SMS verification failed for phone {$phone}, reason: {$reason}, but no employer_id available", 'employer');
        }
    }

    /**
     * Логировать завершение регистрации
     * 
     * @param Employer $employer
     * @param array $profileData
     */
    public function logRegistrationCompleted(Employer $employer, array $profileData)
    {
        $this->logAction(
            $employer->id,
            EmployerLog::ACTION_PROFILE_CREATED,
            [
                'profile_fields' => array_keys($profileData),
                'business_name' => $profileData['business_name'] ?? null
            ]
        );
    }

    /**
     * Логировать ошибку
     * 
     * @param int|null $employerId
     * @param string $errorMessage
     * @param array $context
     */
    public function logError($employerId, string $errorMessage, array $context = [])
    {
        $this->logAction(
            $employerId,
            EmployerLog::ACTION_ERROR_OCCURRED,
            array_merge($context, ['error_message' => $errorMessage])
        );
    }

    /**
     * Получить логи работодателя
     * 
     * @param int $employerId
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getEmployerLogs(int $employerId, int $limit = 20, int $offset = 0)
    {
        return EmployerLog::find()
            ->where(['employer_id' => $employerId])
            ->orderBy('created_at DESC')
            ->limit($limit)
            ->offset($offset)
            ->all();
    }

    /**
     * Получить статистику активности работодателя
     * 
     * @param int $employerId
     * @param int $days
     * @return array
     */
    public function getActivityStats(int $employerId, int $days = 30)
    {
        $fromDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));

        $totalActions = EmployerLog::find()
            ->where(['employer_id' => $employerId])
            ->andWhere(['>=', 'created_at', $fromDate])
            ->count();

        $actionTypes = EmployerLog::find()
            ->select(['action', 'COUNT(*) as count'])
            ->where(['employer_id' => $employerId])
            ->andWhere(['>=', 'created_at', $fromDate])
            ->groupBy('action')
            ->orderBy('count DESC')
            ->asArray()
            ->all();

        return [
            'total_actions' => $totalActions,
            'period_days' => $days,
            'action_breakdown' => $actionTypes
        ];
    }
}
